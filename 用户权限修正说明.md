# 用户权限修正说明

## 问题描述

在之前的实现中，前台App列表显示的是所有用户的应用，而不是当前用户的应用。这不符合业务逻辑，应该是：
- **前台App列表**: 只显示当前用户的应用
- **后台管理**: 显示所有用户的应用（管理员权限）

## 修正内容

### 1. 前台App控制器修正

#### **修正前**
```php
// 查询App列表 - 显示所有用户的应用
$list = AppModel::withSearch(['title', 'appCode', 'isOnline', 'isPrivate'], $search)
    ->order('createTime', 'desc')
    ->paginate([
        'list_rows' => 12,
        'query' => $request->param(),
    ]);
```

#### **修正后**
```php
// 查询当前用户的App列表 - 只显示当前用户的应用
$list = AppModel::withSearch(['title', 'appCode', 'isOnline', 'isPrivate'], $search)
    ->where('salerUserId', $request->userId) // 只查询当前用户的应用
    ->order('createTime', 'desc')
    ->paginate([
        'list_rows' => 12,
        'query' => $request->param(),
    ]);
```

### 2. 后台管理确认

#### **Admin控制器保持不变**
- `reviewCenter()` 方法查询所有用户的数据
- `index()` 方法统计所有用户的数据
- 这是正确的，因为管理员需要管理所有用户的应用

## 权限体系说明

### 1. 用户身份区分

#### **普通用户 (前台)**
- **中间件**: `Auth` - 设置 `$request->userId = 1`
- **权限范围**: 只能操作自己的应用
- **数据过滤**: 通过 `where('salerUserId', $request->userId)` 限制

#### **管理员 (后台)**
- **中间件**: `AdminAuth` - 设置 `$request->adminUserId = 1` 和 `$request->isAdmin = true`
- **权限范围**: 可以查看和操作所有用户的应用
- **数据过滤**: 不限制用户，查询所有数据

### 2. 访问控制

#### **前台路由**
```
/Saler/app/index          # 当前用户的应用列表
/Saler/app/detail/{id}    # 当前用户的应用详情
/Saler/app/edit/{id}      # 当前用户的应用编辑
```

#### **后台路由**
```
/Saler/admin/index        # 管理员概览页面
/Saler/admin/reviewCenter # 所有用户的审核中心
/Saler/admin/detail/{id}  # 任意用户的应用详情
```

### 3. 数据查询范围

#### **前台查询**
- **App列表**: `WHERE salerUserId = {当前用户ID}`
- **App详情**: `WHERE appId = {ID} AND salerUserId = {当前用户ID}`
- **AppBuild**: `WHERE appId IN (当前用户的应用ID列表)`
- **AppRuntime**: `WHERE appId IN (当前用户的应用ID列表)`

#### **后台查询**
- **App列表**: 无用户限制，查询所有应用
- **App详情**: 无用户限制，可查看任意应用
- **审核中心**: 查询所有用户的待审核项目
- **统计数据**: 统计所有用户的数据

## 中间件配置

### 1. Auth中间件 (普通用户)

```php
class Auth
{
    public function handle(Request $request, Closure $next): Response
    {
        // 模拟当前用户ID为1
        $request->userId = 1;
        View::assign(['userId' => $request->userId]);
        return $next($request);
    }
}
```

### 2. AdminAuth中间件 (管理员)

```php
class AdminAuth
{
    public function handle(Request $request, Closure $next): Response
    {
        // 模拟管理员用户ID为1
        $adminUserId = 1;
        
        // 检查是否有管理员权限
        if (!$adminUserId) {
            throw new HttpException(403, '无管理员权限');
        }
        
        // 将管理员信息注入到请求中
        $request->adminUserId = $adminUserId;
        $request->isAdmin = true;
        
        View::assign([
            'adminUserId' => $adminUserId,
            'isAdmin' => true,
        ]);
        
        return $next($request);
    }
}
```

### 3. AppCheck中间件 (应用权限检查)

```php
class AppCheck
{
    public function handle(Request $request, Closure $next): Response
    {
        $appId = $request->param('appId');
        
        if (!$appId) {
            throw new HttpException(400, '缺少应用ID');
        }
        
        $app = AppModel::find($appId);
        
        if (!$app) {
            throw new HttpException(404, '应用不存在');
        }
        
        // 检查应用是否属于当前用户
        if ($app->salerUserId != $request->userId) {
            throw new HttpException(403, '无权限访问此应用');
        }
        
        $request->app = $app;
        return $next($request);
    }
}
```

## 业务逻辑说明

### 1. 前台业务逻辑

#### **用户只能操作自己的应用**
- 查看：只能查看自己创建的应用
- 编辑：只能编辑自己的应用
- 删除：只能删除自己的应用
- 创建：新创建的应用自动归属于当前用户

#### **数据隔离**
- 每个用户只能看到自己的数据
- 通过 `salerUserId` 字段进行数据隔离
- AppCheck中间件确保用户不能访问其他用户的应用

### 2. 后台业务逻辑

#### **管理员可以管理所有应用**
- 查看：可以查看所有用户的应用
- 审核：可以审核所有用户的应用、应用包、运行环境
- 统计：统计所有用户的数据
- 操作：可以对任意应用进行上线/下线操作

#### **全局视图**
- 审核中心显示所有待审核项目
- 统计数据包含所有用户的数据
- 不受用户权限限制

## 安全考虑

### 1. 权限验证

#### **前台权限**
- Auth中间件验证用户身份
- AppCheck中间件验证应用归属
- 数据查询时添加用户ID限制

#### **后台权限**
- AdminAuth中间件验证管理员身份
- 只有管理员可以访问后台功能
- 管理员操作不受用户限制

### 2. 数据安全

#### **前台数据安全**
- 所有查询都添加用户ID限制
- 防止用户访问其他用户的数据
- 中间件层面进行权限检查

#### **后台数据安全**
- 管理员身份验证
- 操作日志记录（可扩展）
- 敏感操作确认机制

## 测试建议

### 1. 前台测试

#### **权限测试**
- 测试用户只能看到自己的应用
- 测试用户不能访问其他用户的应用
- 测试AppCheck中间件的权限验证

#### **功能测试**
- 测试应用的创建、编辑、删除
- 测试搜索和筛选功能
- 测试分页功能

### 2. 后台测试

#### **管理员权限测试**
- 测试管理员可以查看所有应用
- 测试审核功能对所有用户生效
- 测试统计数据的准确性

#### **审核功能测试**
- 测试单个审核功能
- 测试批量审核功能
- 测试混合类型审核

## 总结

通过这次修正，实现了正确的权限分离：

1. **前台用户**: 只能操作自己的应用，数据完全隔离
2. **后台管理员**: 可以管理所有用户的应用，具有全局权限
3. **安全保障**: 通过中间件和数据查询限制确保数据安全
4. **业务逻辑**: 符合实际的应用管理需求

这样的权限体系既保证了用户数据的安全性，又满足了管理员的管理需求。
