{extend name="common/base" /}

{block name="title"}审核中心 - 后台管理 - 应用市场管理系统{/block}

{block name="content"}
<div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="iconfont icon-review text-indigo-600 mr-2"></i>
                        审核中心
                    </h1>
                    <span class="ml-4 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">管理员</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{__MPRE__}{:url('admin/index')}" class="text-gray-600 hover:text-indigo-600 text-sm">
                        <i class="iconfont icon-back mr-1"></i>应用管理
                    </a>
                    <a href="{__MPRE__}{:url('app/index')}" class="text-gray-600 hover:text-indigo-600 text-sm">
                        <i class="iconfont icon-home mr-1"></i>返回前台
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-review text-2xl text-orange-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">待审核总数</p>
                        <p class="text-2xl font-semibold text-orange-600">{$stats.total}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-app text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">应用审核</p>
                        <p class="text-2xl font-semibold text-blue-600">{$stats.app}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-package text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">应用包审核</p>
                        <p class="text-2xl font-semibold text-green-600">{$stats.build}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-runtime text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">运行环境审核</p>
                        <p class="text-2xl font-semibold text-purple-600">{$stats.runtime}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">搜索筛选</h3>
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核类型</label>
                        <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">全部类型</option>
                            <option value="app" {if $search.type == 'app'}selected{/if}>应用</option>
                            <option value="build" {if $search.type == 'build'}selected{/if}>应用包</option>
                            <option value="runtime" {if $search.type == 'runtime'}selected{/if}>运行环境</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核状态</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="1" {if $search.status == '1'}selected{/if}>审核中</option>
                            <option value="0" {if $search.status == '0'}selected{/if}>编辑中</option>
                            <option value="2" {if $search.status == '2'}selected{/if}>已通过</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">关键词</label>
                        <input type="text" name="keyword" value="{$search.keyword}" placeholder="搜索标题或代码"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="iconfont icon-search mr-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">全选</span>
                        </label>
                        <span id="selectedCount" class="text-sm text-gray-500">已选择 0 项</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="batchReviewItems('approve')" class="px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:opacity-50" disabled id="batchApproveBtn">
                            <i class="iconfont icon-check mr-1"></i>批量通过
                        </button>
                        <button type="button" onclick="batchReviewItems('reject')" class="px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:opacity-50" disabled id="batchRejectBtn">
                            <i class="iconfont icon-close mr-1"></i>批量拒绝
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- 表格头部 -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="iconfont icon-list mr-2 text-indigo-600"></i>
                        审核列表
                        <span class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                            共 {$list->total()} 项待审核
                        </span>
                    </h3>
                    <div class="flex items-center space-x-3">
                        <!-- 刷新按钮 -->
                        <button type="button" onclick="window.location.reload()" class="px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-all duration-200">
                            <i class="iconfont icon-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表格内容 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left">
                                <div class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 focus:ring-offset-0">
                                    <label class="ml-2 text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">全选</label>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-type text-gray-400"></i>
                                    <span>类型</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-app text-gray-400"></i>
                                    <span>项目信息</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-user text-gray-400"></i>
                                    <span>开发者</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-status text-gray-400"></i>
                                    <span>状态</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-time text-gray-400"></i>
                                    <span>提交时间</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-setting text-gray-400"></i>
                                    <span>操作</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {volist name="list" id="item"}
                        <tr class="hover:bg-blue-50 transition-colors duration-200 group">
                            <td class="px-6 py-5 whitespace-nowrap">
                                <input type="checkbox" class="item-checkbox rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 focus:ring-offset-0"
                                       value="{$item.type}:{$item.id}" data-type="{$item.type}" data-id="{$item.id}" {if $item.status != 1}disabled{/if}>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                {if $item.type == 'app'}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                    <i class="iconfont icon-app mr-1.5"></i>应用
                                </span>
                                {elseif $item.type == 'build'}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                                    <i class="iconfont icon-package mr-1.5"></i>应用包
                                </span>
                                {else}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 border border-purple-200">
                                    <i class="iconfont icon-runtime mr-1.5"></i>运行环境
                                </span>
                                {/if}
                            </td>
                            <td class="px-6 py-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-14 w-14 relative">
                                        {if $item.logo}
                                        <img class="h-14 w-14 rounded-xl object-cover shadow-sm border border-gray-200" src="//i.cdn.yimenapp.com/{$item.logo}" alt="{$item.title}">
                                        {else}
                                        <div class="h-14 w-14 rounded-xl bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center shadow-sm border border-gray-200">
                                            <i class="iconfont icon-{$item.type} text-indigo-500 text-2xl"></i>
                                        </div>
                                        {/if}
                                        <!-- 状态指示器 -->
                                        <div class="absolute -top-1 -right-1">
                                            {if $item.status == 1}
                                            <div class="w-4 h-4 bg-yellow-400 rounded-full border-2 border-white shadow-sm animate-pulse" title="审核中"></div>
                                            {elseif $item.status == 2}
                                            <div class="w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm" title="已通过"></div>
                                            {else}
                                            <div class="w-4 h-4 bg-gray-300 rounded-full border-2 border-white shadow-sm" title="编辑中"></div>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <div class="flex items-center space-x-2">
                                            <h4 class="text-sm font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">{$item.title}</h4>
                                            {if isset($item.isPrivate) && $item.isPrivate}
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="iconfont icon-lock mr-1"></i>私有
                                            </span>
                                            {/if}
                                        </div>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <code class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{$item.code}</code>
                                            {if $item.description}
                                            <span class="text-xs text-gray-500 truncate max-w-xs" title="{$item.description}">{$item.description|mb_substr=0,30,'utf-8'}...</span>
                                            {/if}
                                        </div>
                                        {if isset($item.versionName)}
                                        <div class="mt-1">
                                            <span class="text-xs text-indigo-600 bg-indigo-50 px-2 py-1 rounded">v{$item.versionName}</span>
                                        </div>
                                        {/if}
                                        {if isset($item.runtimeName)}
                                        <div class="mt-1">
                                            <span class="text-xs text-purple-600 bg-purple-50 px-2 py-1 rounded">{$item.runtimeName}</span>
                                        </div>
                                        {/if}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="iconfont icon-user text-gray-500 text-sm"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">用户 {$item.salerUserId}</div>
                                        <div class="text-xs text-gray-500">开发者</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="flex flex-col space-y-2">
                                    {if $item.status == 0}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                        <i class="iconfont icon-edit mr-1.5"></i>编辑中
                                    </span>
                                    {elseif $item.status == 1}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                        <i class="iconfont icon-clock mr-1.5"></i>审核中
                                    </span>
                                    {else}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                                        <i class="iconfont icon-check-circle mr-1.5"></i>已通过
                                    </span>
                                    {/if}

                                    {if $item.statusMessage}
                                    <div class="text-xs text-gray-500 max-w-xs truncate" title="{$item.statusMessage}">
                                        {$item.statusMessage}
                                    </div>
                                    {/if}
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{$item.createTime|date='Y-m-d'}</div>
                                <div class="text-xs text-gray-500">{$item.createTime|date='H:i:s'}</div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    {if $item.status == 1}
                                    <!-- 审核操作 -->
                                    <button type="button" onclick="reviewItem('{$item.type}', {$item.id}, 'approve')"
                                            class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200">
                                        <i class="iconfont icon-check mr-1"></i>通过
                                    </button>
                                    <button type="button" onclick="reviewItem('{$item.type}', {$item.id}, 'reject')"
                                            class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200">
                                        <i class="iconfont icon-close mr-1"></i>拒绝
                                    </button>
                                    {/if}

                                    <!-- 查看详情 -->
                                    {if $item.type == 'app'}
                                    <a href="{__MPRE__}{:url('admin/detail')}?appId={$item.id}"
                                       class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors duration-200">
                                        <i class="iconfont icon-eye mr-1"></i>详情
                                    </a>
                                    {elseif $item.type == 'build'}
                                    <a href="{__MPRE__}{:url('appBuild/edit')}?appBuildId={$item.id}"
                                       class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors duration-200">
                                        <i class="iconfont icon-eye mr-1"></i>详情
                                    </a>
                                    {else}
                                    <a href="{__MPRE__}{:url('appRuntime/detail')}?appId={$item.appId}&runtimeId={$item.runtimeId}"
                                       class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors duration-200">
                                        <i class="iconfont icon-eye mr-1"></i>详情
                                    </a>
                                    {/if}
                                </div>
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>

                <!-- 空状态 -->
                {empty name="list"}
                <div class="text-center py-12">
                    <i class="iconfont icon-empty text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无待审核项目</h3>
                    <p class="text-gray-500">当前筛选条件下没有找到任何待审核项目</p>
                </div>
                {/empty}
            </div>

            <!-- 分页 -->
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-700">
                        <span>显示第 {$list->currentPage()} 页，共 {$list->lastPage()} 页</span>
                        <span class="mx-2">•</span>
                        <span>每页 {$list->listRows()} 条，共 {$list->total()} 条记录</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        {$list->render()}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initCheckboxes();
});

// 初始化复选框功能
function initCheckboxes() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox:not([disabled])');
    const selectedCountSpan = document.getElementById('selectedCount');
    const batchApproveBtn = document.getElementById('batchApproveBtn');
    const batchRejectBtn = document.getElementById('batchRejectBtn');

    // 全选/取消全选
    selectAllCheckbox.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // 单个复选框变化
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
        });
    });

    // 更新选中数量和按钮状态
    function updateSelectedCount() {
        const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountSpan.textContent = `已选择 ${count} 项`;

        // 更新批量操作按钮状态
        batchApproveBtn.disabled = count === 0;
        batchRejectBtn.disabled = count === 0;
    }
}

// 审核单个项目
async function reviewItem(type, id, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';
    const typeText = type === 'app' ? '应用' : (type === 'build' ? '应用包' : '运行环境');

    // 如果是拒绝，询问拒绝理由
    let message = '';
    if (action === 'reject') {
        message = prompt('请输入拒绝理由（可选）：');
        if (message === null) return; // 用户取消
    }

    if (!confirm(`确定要${actionText}这个${typeText}吗？`)) {
        return;
    }

    try {
        const response = await fetch('{__MPRE__}{:url("admin/reviewItem")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                type: type,
                id: id,
                action: action,
                message: message
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 批量审核
async function batchReviewItems(action) {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const items = Array.from(checkedBoxes).map(cb => ({
        type: cb.dataset.type,
        id: cb.dataset.id
    }));

    if (items.length === 0) {
        showNotification('请选择要审核的项目', 'warning');
        return;
    }

    const actionText = action === 'approve' ? '通过' : '拒绝';

    // 如果是拒绝，询问拒绝理由
    let message = '';
    if (action === 'reject') {
        message = prompt('请输入拒绝理由（可选）：');
        if (message === null) return; // 用户取消
    }

    if (!confirm(`确定要批量${actionText} ${items.length} 个项目吗？`)) {
        return;
    }

    try {
        const response = await fetch('{__MPRE__}{:url("admin/batchReviewItems")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                items: JSON.stringify(items),
                action: action,
                message: message
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            if (data.details && data.details.length > 0) {
                console.warn('批量操作详情:', data.details);
            }
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        type === 'warning' ? 'icon-warning' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 表格悬停效果 */
tbody tr:hover {
    background-color: #eff6ff;
}

/* 复选框样式优化 */
input[type="checkbox"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 状态指示器动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐变背景 */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* 按钮悬停效果 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* 表格行高亮 */
tbody tr.group:hover {
    background: linear-gradient(90deg, #eff6ff 0%, #dbeafe 50%, #eff6ff 100%);
}

/* 文本截断 */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-5 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
    height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>
{/block}
