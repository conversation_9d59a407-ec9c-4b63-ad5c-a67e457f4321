{extend name="common/base" /}

{block name="title"}应用管理 - 后台管理 - 应用市场管理系统{/block}

{block name="content"}
<div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="iconfont icon-app text-indigo-600 mr-2"></i>
                        应用管理
                    </h1>
                    <span class="ml-4 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">管理员</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{__MPRE__}{:url('admin/reviewCenter')}" class="px-3 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors duration-200">
                        <i class="iconfont icon-review mr-1"></i>审核中心
                    </a>
                    <a href="{__MPRE__}{:url('admin/index')}" class="text-gray-600 hover:text-indigo-600 text-sm">
                        <i class="iconfont icon-back mr-1"></i>返回概览
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-app text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">总应用数</p>
                        <p class="text-2xl font-semibold text-gray-900">{$stats.total}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6 border-l-4 border-yellow-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-clock text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">待审核</p>
                        <p class="text-2xl font-semibold text-yellow-600">{$stats.pending}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-check-circle text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已通过</p>
                        <p class="text-2xl font-semibold text-green-600">{$stats.approved}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6 border-l-4 border-gray-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-edit text-2xl text-gray-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">编辑中</p>
                        <p class="text-2xl font-semibold text-gray-600">{$stats.draft}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6 border-l-4 border-indigo-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-online text-2xl text-indigo-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已上线</p>
                        <p class="text-2xl font-semibold text-indigo-600">{$stats.online}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6 border-l-4 border-red-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-lock text-2xl text-red-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">私有应用</p>
                        <p class="text-2xl font-semibold text-red-600">{$stats.private}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">搜索筛选</h3>
                <form method="GET" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">应用名称</label>
                        <input type="text" name="title" value="{$search.title}" placeholder="搜索应用名称"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">应用代码</label>
                        <input type="text" name="appCode" value="{$search.appCode}" placeholder="搜索应用代码"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核状态</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">全部状态</option>
                            <option value="0" {if $search.status == '0'}selected{/if}>编辑中</option>
                            <option value="1" {if $search.status == '1'}selected{/if}>审核中</option>
                            <option value="2" {if $search.status == '2'}selected{/if}>审核通过</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">上线状态</label>
                        <select name="isOnline" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">全部</option>
                            <option value="1" {if $search.isOnline == '1'}selected{/if}>已上线</option>
                            <option value="0" {if $search.isOnline == '0'}selected{/if}>未上线</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">开发者ID</label>
                        <input type="number" name="salerUserId" value="{$search.salerUserId}" placeholder="开发者ID"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="iconfont icon-search mr-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 应用列表 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- 表格头部 -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="iconfont icon-list mr-2 text-indigo-600"></i>
                        应用列表
                        <span class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                            共 {$list->total()} 个应用
                        </span>
                    </h3>
                    <div class="flex items-center space-x-3">
                        <!-- 刷新按钮 -->
                        <button type="button" onclick="window.location.reload()" class="px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-all duration-200">
                            <i class="iconfont icon-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表格内容 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-app text-gray-400"></i>
                                    <span>应用信息</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-user text-gray-400"></i>
                                    <span>开发者</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-status text-gray-400"></i>
                                    <span>状态</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden-mobile">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-data text-gray-400"></i>
                                    <span>数据统计</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden-mobile">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-time text-gray-400"></i>
                                    <span>创建时间</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center space-x-1">
                                    <i class="iconfont icon-setting text-gray-400"></i>
                                    <span>操作</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {volist name="list" id="item"}
                        <tr class="hover:bg-blue-50 transition-colors duration-200 group">
                            <td class="px-6 py-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-14 w-14 relative">
                                        {if $item.logo}
                                        <img class="h-14 w-14 rounded-xl object-cover shadow-sm border border-gray-200" src="//i.cdn.yimenapp.com/{$item.logo}" alt="{$item.title}">
                                        {else}
                                        <div class="h-14 w-14 rounded-xl bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center shadow-sm border border-gray-200">
                                            <i class="iconfont icon-app text-indigo-500 text-2xl"></i>
                                        </div>
                                        {/if}
                                        <!-- 状态指示器 -->
                                        <div class="absolute -top-1 -right-1">
                                            {if $item.status == 1}
                                            <div class="w-4 h-4 bg-yellow-400 rounded-full border-2 border-white shadow-sm animate-pulse" title="审核中"></div>
                                            {elseif $item.status == 2 && $item.isOnline}
                                            <div class="w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm" title="已上线"></div>
                                            {else}
                                            <div class="w-4 h-4 bg-gray-300 rounded-full border-2 border-white shadow-sm" title="未上线"></div>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <div class="flex items-center space-x-2">
                                            <h4 class="text-sm font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">{$item.title}</h4>
                                            {if $item.isPrivate}
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="iconfont icon-lock mr-1"></i>私有
                                            </span>
                                            {/if}
                                        </div>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <code class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{$item.appCode}</code>
                                            {if $item.description}
                                            <span class="text-xs text-gray-500 truncate max-w-xs" title="{$item.description}">{$item.description|mb_substr=0,30,'utf-8'}...</span>
                                            {/if}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="iconfont icon-user text-gray-500 text-sm"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">用户 {$item.salerUserId}</div>
                                        <div class="text-xs text-gray-500">开发者</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="flex flex-col space-y-2">
                                    <!-- 审核状态 -->
                                    {if $item.status == 0}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                        <i class="iconfont icon-edit mr-1.5"></i>编辑中
                                    </span>
                                    {elseif $item.status == 1}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                        <i class="iconfont icon-clock mr-1.5"></i>审核中
                                    </span>
                                    {else}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                                        <i class="iconfont icon-check-circle mr-1.5"></i>已通过
                                    </span>
                                    {/if}

                                    <!-- 上线状态 -->
                                    {if $item.isOnline}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 border border-indigo-200">
                                        <i class="iconfont icon-online mr-1.5"></i>已上线
                                    </span>
                                    {else}
                                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                        <i class="iconfont icon-offline mr-1.5"></i>未上线
                                    </span>
                                    {/if}
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap hidden-mobile">
                                <div class="text-sm text-gray-900">
                                    <div class="flex items-center space-x-4">
                                        <div class="text-center">
                                            <div class="text-lg font-semibold text-indigo-600">{$item.paidCount|default=0}</div>
                                            <div class="text-xs text-gray-500">付费用户</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-lg font-semibold text-green-600">{$item.downloadCount|default=0}</div>
                                            <div class="text-xs text-gray-500">下载量</div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap hidden-mobile">
                                <div class="text-sm text-gray-900">{$item.createTime|date='Y-m-d'}</div>
                                <div class="text-xs text-gray-500">{$item.createTime|date='H:i:s'}</div>
                            </td>
                            <td class="px-6 py-5 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    <!-- 查看详情 -->
                                    <a href="{__MPRE__}{:url('admin/detail', ['appId' => $item.appId])}"
                                       class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors duration-200">
                                        <i class="iconfont icon-eye mr-1"></i>详情
                                    </a>

                                    {if $item.status == 1}
                                    <!-- 审核操作 -->
                                    <button type="button" onclick="reviewApp({$item.appId}, 'approve')"
                                            class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200">
                                        <i class="iconfont icon-check mr-1"></i>通过
                                    </button>
                                    <button type="button" onclick="reviewApp({$item.appId}, 'reject')"
                                            class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200">
                                        <i class="iconfont icon-close mr-1"></i>拒绝
                                    </button>
                                    {/if}

                                    {if $item.status == 2}
                                    <!-- 上线/下线操作 -->
                                    <button type="button" onclick="updateStatus({$item.appId}, {$item.isOnline ? 0 : 1})"
                                            class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200">
                                        <i class="iconfont icon-{$item.isOnline ? 'offline' : 'online'} mr-1"></i>{$item.isOnline ? '下线' : '上线'}
                                    </button>
                                    {/if}
                                </div>
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>

                <!-- 空状态 -->
                {empty name="list"}
                <div class="text-center py-12">
                    <i class="iconfont icon-empty text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无应用</h3>
                    <p class="text-gray-500">当前筛选条件下没有找到任何应用</p>
                </div>
                {/empty}
            </div>

            <!-- 分页 -->
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-700">
                        <span>显示第 {$list->currentPage()} 页，共 {$list->lastPage()} 页</span>
                        <span class="mx-2">•</span>
                        <span>每页 {$list->listRows()} 条，共 {$list->total()} 条记录</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        {$list->render()}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加表格行悬停效果
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(2px)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
});

// 审核应用
async function reviewApp(appId, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';

    // 如果是拒绝，询问拒绝理由
    let message = '';
    if (action === 'reject') {
        message = prompt('请输入拒绝理由（可选）：');
        if (message === null) return; // 用户取消
    }

    if (!confirm(`确定要${actionText}这个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('{__MPRE__}{:url("admin/review")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appId: appId,
                action: action,
                message: message
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 更新应用状态（上线/下线）
async function updateStatus(appId, isOnline) {
    const statusText = isOnline ? '上线' : '下线';

    if (!confirm(`确定要${statusText}这个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('{__MPRE__}{:url("admin/updateStatus")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appId: appId,
                isOnline: isOnline
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        type === 'warning' ? 'icon-warning' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 表格悬停效果 */
tbody tr:hover {
    background-color: #eff6ff;
}

/* 状态指示器动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐变背景 */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* 按钮悬停效果 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* 表格行高亮 */
tbody tr.group:hover {
    background: linear-gradient(90deg, #eff6ff 0%, #dbeafe 50%, #eff6ff 100%);
}

/* 文本截断 */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .hidden-mobile {
        display: none;
    }

    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-5 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
    height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 卡片边框效果 */
.border-l-4 {
    border-left-width: 4px;
}

/* 统计卡片动画 */
.stats-card {
    transform: translateY(0);
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>
{/block}
