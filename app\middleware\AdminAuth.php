<?php
declare (strict_types = 1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\View;
use think\exception\HttpException;

/**
 * 管理员鉴权中间件
 * 验证管理员权限
 */
class AdminAuth
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 模拟管理员用户ID为1（实际项目中应该从session或token中获取）
        $adminUserId = 1; // $_SERVER["HTTP_YM_ADMIN_USERID"] ?? null;
        
        // 检查是否有管理员权限
        if (!$adminUserId) {
            throw new HttpException(403, '无管理员权限');
        }
        
        // 将管理员信息注入到请求中
        $request->adminUserId = $adminUserId;
        $request->isAdmin = true;
        
        // 模板赋值
        View::assign([
            'adminUserId' => $adminUserId,
            'isAdmin' => true,
        ]);
        
        return $next($request);
    }
}
