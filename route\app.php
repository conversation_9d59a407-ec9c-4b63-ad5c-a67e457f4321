<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 首页路由
Route::get('/', 'Market/index');
Route::group('Saler', function () {
    // App路由
    Route::get('/', 'App/index');
    Route::group('app', function () {
        Route::get('/', 'App/index');
        Route::get('/index', 'App/index');
        Route::get('create', 'App/create');
        Route::post('save', 'App/save');
        Route::get('detail', 'App/detail');
        Route::get('edit', 'App/edit');
        Route::post('update', 'App/update');
        Route::post('delete', 'App/delete');
    });

    // AppRuntime路由
    Route::group('appRuntime', function () {
        Route::get('edit', 'AppRuntime/edit');
        Route::post('update', 'AppRuntime/update');
        Route::get('detail', 'AppRuntime/detail');
    });

    // AppRelease路由
    Route::group('appRelease', function () {
        Route::get('index', 'AppRelease/index');
        Route::post('save', 'AppRelease/save');
        Route::post('updateStatus', 'AppRelease/updateStatus');
        Route::post('delete', 'AppRelease/delete');
    });

    // AppBuild路由
    Route::group('appBuild', function () {
        Route::get('index', 'AppBuild/index');
        Route::get('create', 'AppBuild/create');
        Route::post('save', 'AppBuild/save');
        Route::get('edit', 'AppBuild/edit');
        Route::post('update', 'AppBuild/update');
        Route::post('delete', 'AppBuild/delete');
    });

    // AppSku路由
    Route::group('appSku', function () {
        Route::get('index', 'AppSku/index');
        Route::get('create', 'AppSku/create');
        Route::post('save', 'AppSku/save');
        Route::get('edit', 'AppSku/edit');
        Route::post('update', 'AppSku/update');
        Route::post('delete', 'AppSku/delete');
    });

    // 后台管理路由
    Route::group('admin', function () {
        Route::get('index', 'Admin/index');
        Route::get('detail', 'Admin/detail');
        Route::post('review', 'Admin/review');
        Route::post('batchReview', 'Admin/batchReview');
        Route::post('updateStatus', 'Admin/updateStatus');

        // 审核中心
        Route::get('reviewCenter', 'Admin/reviewCenter');
        Route::post('reviewBuild', 'Admin/reviewBuild');
        Route::post('reviewRuntime', 'Admin/reviewRuntime');
        Route::post('reviewItem', 'Admin/reviewItem');
        Route::post('batchReviewItems', 'Admin/batchReviewItems');
    });
});


// 市场路由（无需鉴权）
Route::group('Buyer', function () {
    Route::get('/', 'Market/index');
    Route::get('detail', 'Market/detail');
});
