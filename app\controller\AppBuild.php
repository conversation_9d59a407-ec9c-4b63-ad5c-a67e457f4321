<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\AppBuild as AppBuildModel;
use app\validate\AppBuild as AppBuildValidate;
use think\exception\ValidateException;

/**
 * AppBuild控制器
 */
class AppBuild extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\Auth',
        'app\\middleware\\AppCheck',
    ];

    /**
     * 应用包列表
     */
    public function index(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取搜索参数
        $search = [
            'versionCode' => $request->param('versionCode', ''),
            'versionName' => $request->param('versionName', ''),
        ];

        // 查询应用包列表
        $list = AppBuildModel::where('appId', $app->appId)
            ->withSearch(['versionCode', 'versionName'], $search)
            ->order('versionCode', 'desc')
            ->paginate([
                'list_rows' => 10,
                'query' => $request->param(),
            ]);

        // 模板赋值
        View::assign([
            'app' => $app,
            'list' => $list,
            'search' => $search,
            'active' => 'build',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 创建应用包页面
     */
    public function create(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 模板赋值
        View::assign([
            'app' => $app,
            'active' => 'build',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 保存应用包
     */
    public function save(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取参数
        $data = $request->post();
        $data['appId'] = $app->appId;

        // 验证数据
        try {
            validate(AppBuildValidate::class)
                ->scene('create')
                ->check($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        // 创建应用包
        $appBuild = new AppBuildModel();
        $appBuild->save($data);

        // 返回结果
        return $this->success('创建成功', ['appBuildId' => $appBuild->appBuildId]);
    }

    /**
     * 编辑应用包页面
     */
    public function edit(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取应用包ID
        $appBuildId = $request->param('appBuildId');

        // 查询应用包信息
        $appBuild = AppBuildModel::where('appId', $app->appId)
            ->where('appBuildId', $appBuildId)
            ->find();

        if (!$appBuild) {
            return $this->error('应用包不存在');
        }

        // 模板赋值
        View::assign([
            'app' => $app,
            'appBuild' => $appBuild,
            'active' => 'build',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 更新应用包
     */
    public function update(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取应用包ID
        $appBuildId = $request->param('appBuildId');

        // 查询应用包信息
        $appBuild = AppBuildModel::where('appId', $app->appId)
            ->where('appBuildId', $appBuildId)
            ->find();

        if (!$appBuild) {
            return json(['code' => 0, 'msg' => '应用包不存在']);
        }

        // 获取参数
        $data = $request->post();
        $data['appId'] = $app->appId;

        // 验证数据
        try {
            validate(AppBuildValidate::class)
                ->scene('edit')
                ->check($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        // 更新应用包
        $appBuild->save($data);

        // 返回结果
        return $this->success('更新成功');
    }

    /**
     * 删除应用包
     */
    public function delete(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取应用包ID
        $appBuildId = $request->param('appBuildId');

        // 查询应用包信息
        $appBuild = AppBuildModel::where('appId', $app->appId)
            ->where('appBuildId', $appBuildId)
            ->find();

        if (!$appBuild) {
            return $this->error('应用包不存在');
        }

        // 删除应用包
        $appBuild->delete();

        // 返回结果
        return $this->success('删除成功');
    }
}
