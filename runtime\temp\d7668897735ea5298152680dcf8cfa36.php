<?php /*a:5:{s:50:"E:\Ym\marketAdmin\app\view\app_runtime\detail.html";i:1747708336;s:43:"E:\Ym\marketAdmin\app\view\common\base.html";i:1747638572;s:45:"E:\Ym\marketAdmin\app\view\common\header.html";i:1747708602;s:46:"E:\Ym\marketAdmin\app\view\common\sidebar.html";i:1747708336;s:45:"E:\Ym\marketAdmin\app\view\common\footer.html";i:1747638354;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title><?php echo htmlentities((string) $app['title']); ?> - <?php echo htmlentities((string) $runtimeName); ?>环境详情 - 应用市场管理系统</title>
    <script src="/static/js/tw.js"></script>
    <link rel="stylesheet" href="/static/css/iconfont.css">
    <style>
        /* 基础响应式样式 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }
        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表格响应式样式 */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .touch-action-manipulation {
                touch-action: manipulation;
            }
            .tap-highlight-transparent {
                -webkit-tap-highlight-color: transparent;
            }
        }
    </style>
    
<link rel="stylesheet" href="/static/css/bytemd.css">
<?php if(isset($ymPlugins['icon_css'])): ?>
<link rel="stylesheet" href="<?php echo htmlentities((string) (isset($ymPlugins['icon_css']) && ($ymPlugins['icon_css'] !== '')?$ymPlugins['icon_css']:'{__MSTATIC__)); ?>/css/iconfont.css'}">
<?php endif; ?>
<style>
.plugin-card {
    transition: all 0.3s ease;
}
.plugin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.plugin-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f3f4f6;
    color: #4f46e5;
}
/* 表格滚动样式 */
.max-h-96 {
    max-height: 24rem;
}
/* 确保表头在滚动时保持固定 */
.sticky {
    position: sticky;
}
.top-0 {
    top: 0;
}
.z-10 {
    z-index: 10;
}
/* 表头背景色，确保滚动时不透明 */
thead.sticky {
    background-color: #f9fafb;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
</style>

</head>
<body class="bg-gray-100 min-h-screen touch-action-manipulation tap-highlight-transparent">
    <!-- 移动端菜单按钮 -->
    <div id="mobile-menu-button" class="fixed z-50 bottom-4 right-4 md:hidden bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
        <i class="iconfont icon-menu text-xl"></i>
    </div>

    <!-- 头部 -->
    

    <!-- 主体内容 -->
    <div class="container mx-auto px-4 py-6">
        
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div id="sidebar" class="bg-white shadow rounded-lg p-4 w-full md:w-64 transition-all duration-300 ease-in-out">
    <!-- 移动端侧边栏标题栏 -->
    <div class="flex justify-between items-center mb-4 md:hidden">
        <h2 class="text-lg font-semibold text-gray-700 truncate"><?php echo htmlentities((string) $app['title']); ?></h2>
        <button id="close-sidebar" class="text-gray-500 hover:text-gray-700 p-1">
            <i class="iconfont icon-close"></i>
        </button>
    </div>

    <div class="mb-4">
        <h2 class="text-lg font-semibold text-gray-700 truncate hidden md:block"><?php echo htmlentities((string) $app['title']); ?></h2>
        <p class="text-sm text-gray-500">AppID: <?php echo htmlentities((string) $app['appId']); ?></p>
    </div>
    <nav>
        <ul class="space-y-2">
            <li>
                <a href="<?php echo url('app/detail', ['appId' => $app['appId']], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 <?php if($active=='detail'): ?>bg-gray-100<?php endif; ?>">
                    <i class="iconfont icon-detail mr-2"></i>
                    <span>应用详情</span>
                </a>
            </li>
            <?php 
            // 获取YmService配置
            $ymConfig = \app\service\YmService::getConfig();

            // 提取运行时环境
            $ym_runtimes = [];
            foreach ($ymConfig['runtime'] as $runtimeGroup) {
                foreach ($runtimeGroup['runtime'] as $runtime) {
                    $ym_runtimes[] = $runtime;
                }
            }
             if(isset($ymConfig['icon'])): ?>
            <link rel="stylesheet" href="<?php echo htmlentities((string) $ymConfig['icon']); ?>">
            <?php endif; if(is_array($ymConfig['runtime']) || $ymConfig['runtime'] instanceof \think\Collection || $ymConfig['runtime'] instanceof \think\Paginator): $i = 0; $__LIST__ = $ymConfig['runtime'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$gruntime): $mod = ($i % 2 );++$i;?>
            <li>
                <div class="sidebar-toggle flex items-center justify-between px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 cursor-pointer">
                    <div class="flex items-center">
                        <i class="iconfont <?php echo htmlentities((string) $gruntime['icon']); ?> mr-2"></i>
                        <span><?php echo htmlentities((string) $gruntime['label']); ?>运行时</span>
                    </div>
                    <i class="iconfont icon-down transform transition-transform duration-200 <?php if($active=='runtime'): ?>rotate-180<?php endif; ?>"></i>
                </div>
                <?php $hiddenname = 'hidden'; if(is_array($gruntime['runtime']) || $gruntime['runtime'] instanceof \think\Collection || $gruntime['runtime'] instanceof \think\Paginator): $i = 0; $__LIST__ = $gruntime['runtime'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$runtime): $mod = ($i % 2 );++$i;if(isset($runtimeId) && $runtimeId == $runtime['id']): $hiddenname = ''; ?>
                    <?php endif; ?>
                <?php endforeach; endif; else: echo "" ;endif; ?>
                <ul class="pl-4 md:pl-8 mt-1 space-y-1 <?php echo htmlentities((string) $hiddenname); ?> transition-all duration-300 ease-in-out">
                    <?php if(is_array($gruntime['runtime']) || $gruntime['runtime'] instanceof \think\Collection || $gruntime['runtime'] instanceof \think\Paginator): $i = 0; $__LIST__ = $gruntime['runtime'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$runtime): $mod = ($i % 2 );++$i;?>
                    <li>
                        <a href="<?php echo url('appRuntime/edit', ['appId' => $app['appId'], 'runtimeId' => $runtime['id']], false, false); ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100 <?php if(isset($runtimeId) && $runtimeId==$runtime['id']): ?>bg-gray-100<?php endif; ?>">
                            <i class="iconfont <?php echo htmlentities((string) $runtime['icon']); ?> mr-2 text-gray-500"></i>
                            <span class="truncate"><?php echo htmlentities((string) $runtime['label']); ?>环境</span>
                        </a>
                    </li>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </ul>
            </li>
            <?php endforeach; endif; else: echo "" ;endif; ?>
            <li>
                <a href="<?php echo url('appBuild/index', ['appId' => $app['appId']], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 <?php if($active=='build'): ?>bg-gray-100<?php endif; ?>">
                    <i class="iconfont icon-package mr-2"></i>
                    <span>应用包管理</span>
                </a>
            </li>
            <li>
                <a href="<?php echo url('appSku/index', ['appId' => $app['appId']], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 <?php if($active=='sku'): ?>bg-gray-100<?php endif; ?>">
                    <i class="iconfont icon-sku mr-2"></i>
                    <span>应用SKU管理</span>
                </a>
            </li>
            <li class="mt-4">
                <a href="<?php echo url('app/index', [], false, false); ?>" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <i class="iconfont icon-back mr-2"></i>
                    <span>返回应用列表</span>
                </a>
            </li>
        </ul>
    </nav>
</div>

<!-- 移动端侧边栏遮罩层 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 hidden md:hidden"></div>


    <!-- 主内容区 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex justify-between items-center mb-6">
                <div class="flex items-center">
                    <i class="iconfont <?php echo htmlentities((string) $runtimeIcon); ?> text-indigo-600 text-2xl mr-2"></i>
                    <h2 class="text-xl font-semibold text-gray-800"><?php echo htmlentities((string) $runtimeName); ?>环境详情</h2>
                </div>
                <a href="<?php echo url('appRuntime/edit', ['appId' => $app['appId'], 'runtimeId' => $runtimeId], false, false); ?>" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="iconfont icon-edit"></i> 编辑环境
                </a>
            </div>

            <!-- 环境信息卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 h-3"></div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-700 mb-4">基本信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">环境类型</span>
                                    <span class="text-sm text-gray-800"><?php echo htmlentities((string) $runtimeName); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">上线状态</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo !empty($appRuntime['isOnline']) ? 'bg-green-100 text-green-800'  :  'bg-gray-100 text-gray-800'; ?>">
                                        <i class="iconfont <?php echo !empty($appRuntime['isOnline']) ? 'icon-online'  :  'icon-offline'; ?> mr-1"></i>
                                        <?php echo !empty($appRuntime['isOnline']) ? '已上线'  :  '未上线'; ?>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">审核状态</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php switch($appRuntime['status']): case "0": ?>bg-gray-100 text-gray-800<?php break; case "1": ?>bg-yellow-100 text-yellow-800<?php break; case "2": ?>bg-green-100 text-green-800<?php break; default: ?>bg-gray-100 text-gray-800
                                        <?php endswitch; ?>">
                                        <?php switch($appRuntime['status']): case "0": ?>编辑中<?php break; case "1": ?>审核中<?php break; case "2": ?>审核通过<?php break; default: ?>未知状态
                                        <?php endswitch; ?>
                                    </span>
                                </div>
                                <?php if($appRuntime['demoLink']): ?>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">演示链接</span>
                                    <a href="<?php echo htmlentities((string) $appRuntime['demoLink']); ?>" target="_blank" class="text-sm text-indigo-600 hover:text-indigo-800">
                                        <i class="iconfont icon-link"></i> 查看演示
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-gray-700 mb-4">配置信息</h3>
                            <?php if($appRuntime['configSchema']): ?>
                            <div class="mb-4">
                                <span class="text-sm font-medium text-gray-500">环境级配置表单</span>
                                <pre class="mt-1 bg-gray-50 p-3 rounded-md text-xs text-gray-700 overflow-auto max-h-32 border border-gray-200"><?php echo json_encode($appRuntime['configSchema'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                            </div>
                            <?php endif; if($appRuntime['extra']): ?>
                            <div>
                                <span class="text-sm font-medium text-gray-500">扩展配置</span>
                                <pre class="mt-1 bg-gray-50 p-3 rounded-md text-xs text-gray-700 overflow-auto max-h-32 border border-gray-200"><?php echo json_encode($appRuntime['extra'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 所需插件 -->
            <?php if(!empty($selectedPlugins)): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                <div class="bg-gradient-to-r from-green-500 to-teal-500 h-3"></div>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">所需原生插件</h3>

                    <!-- 搜索和筛选 -->
                    <div class="mb-4 flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <label for="pluginSearch" class="block text-sm font-medium text-gray-700 mb-1">搜索插件</label>
                            <div class="relative">
                                <input type="text" id="pluginSearch" placeholder="输入插件名称或描述..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pl-10">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="iconfont icon-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1">
                            <label for="tagFilter" class="block text-sm font-medium text-gray-700 mb-1">按标签筛选</label>
                            <select id="tagFilter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">全部标签</option>
                                <?php if(isset($ymPlugins['tags']) && !empty($ymPlugins['tags'][0])): if(is_array($ymPlugins['tags'][0]) || $ymPlugins['tags'][0] instanceof \think\Collection || $ymPlugins['tags'][0] instanceof \think\Paginator): $i = 0; $__LIST__ = $ymPlugins['tags'][0];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$tag): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo htmlentities((string) $tag['id']); ?>"><?php echo htmlentities((string) $tag['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>

                    <!-- 插件表格 -->
                    <div class="border rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <div class="max-h-96 overflow-y-auto">
                                <table id="pluginsTable" class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50 sticky top-0 z-10">
                                        <tr>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">插件</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标签</th>
                                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php if(is_array($selectedPlugins) || $selectedPlugins instanceof \think\Collection || $selectedPlugins instanceof \think\Paginator): $i = 0; $__LIST__ = $selectedPlugins;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$plugin): $mod = ($i % 2 );++$i;?>
                                        <tr class="plugin-row" data-plugin-id="<?php echo htmlentities((string) $plugin['id']); ?>" data-plugin-tags="<?php echo implode(',', $plugin['tag_ids']); ?>">
                                            <td class="px-3 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="plugin-icon mr-3">
                                                        <i class="iconfont <?php echo htmlentities((string) $plugin['icon']); ?>"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlentities((string) $plugin['name']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-3 py-4">
                                                <div class="text-sm text-gray-500 line-clamp-2"><?php echo htmlentities((string) $plugin['description']); ?></div>
                                            </td>
                                            <td class="px-3 py-4 whitespace-nowrap">
                                                <?php if(!empty($pluginKey) && isset($plugin[$pluginKey]) && $plugin[$pluginKey] > 0): ?>
                                                <div class="text-sm font-medium text-indigo-600">¥<?php echo htmlentities((string) $plugin[$pluginKey]); ?></div>
                                                <?php else: ?>
                                                <div class="text-sm text-gray-500">无价格</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-3 py-4">
                                                <div class="flex flex-wrap gap-1">
                                                    <?php if(isset($plugin['tag_ids']) && !empty($plugin['tag_ids'])): if(is_array($plugin['tag_ids']) || $plugin['tag_ids'] instanceof \think\Collection || $plugin['tag_ids'] instanceof \think\Paginator): $i = 0; $__LIST__ = $plugin['tag_ids'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$tagId): $mod = ($i % 2 );++$i;if(isset($ymPlugins['tags']) && !empty($ymPlugins['tags'][0])): foreach($ymPlugins['tags'][0] as $tag): if($tag['id'] == $tagId): ?>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                                                                        <?php echo htmlentities((string) $tag['name']); ?>
                                                                    </span>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            <?php endif; ?>
                                                        <?php endforeach; endif; else: echo "" ;endif; else: ?>
                                                        <span class="text-xs text-gray-500">无标签</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo htmlentities((string) $plugin['id']); ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 详情内容 -->
            <?php if($appRuntime['content']): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-blue-500 to-cyan-500 h-3"></div>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">详情内容</h3>
                    <!-- Markdown渲染区域 -->
                    <div id="markdown-content" class="markdown-body"></div>
                </div>
            </div>
            <?php endif; ?>

            <div class="mt-6 flex justify-end">
                <a href="<?php echo url('app/detail', ['appId' => $app['appId']], false, false); ?>" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    返回应用详情
                </a>
            </div>
        </div>
    </div>
</div>

    </div>

    <!-- 底部 -->
    <footer class="bg-white shadow mt-8 py-4">
    <div class="container mx-auto px-4">
        <div class="text-center text-gray-500 text-sm">
            &copy; 2025 应用市场管理系统 
        </div>
    </div>
</footer>


    <script src="/static/js/app.js"></script>
    
<!-- 引入ByteMD渲染器 -->
<script src="https://cdn.jsdelivr.net/npm/bytemd/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-gfm/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-highlight/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-math/dist/index.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@bytemd/plugin-mermaid/dist/index.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Markdown渲染器
    const markdownContent = document.getElementById('markdown-content');
    if (markdownContent) {
        const { Viewer } = bytemd;
        const gfmPlugin = window.bytemdPluginGfm();
        const highlightPlugin = window.bytemdPluginHighlight();
        const mathPlugin = window.bytemdPluginMath();
        const mermaidPlugin = window.bytemdPluginMermaid();

        // 获取Markdown内容
        const content = `<?php echo $appRuntime['content']; ?>`;

        // 创建Viewer实例
        new Viewer({
            target: markdownContent,
            props: {
                value: content,
                plugins: [
                    gfmPlugin,
                    highlightPlugin,
                    mathPlugin,
                    mermaidPlugin
                ]
            }
        });
    }

    // 初始化插件表格搜索和筛选功能
    const pluginSearch = document.getElementById('pluginSearch');
    const tagFilter = document.getElementById('tagFilter');

    if (pluginSearch && tagFilter) {
        // 搜索功能
        pluginSearch.addEventListener('input', filterPlugins);

        // 标签筛选功能
        tagFilter.addEventListener('change', filterPlugins);

        // 筛选插件函数
        function filterPlugins() {
            const searchTerm = pluginSearch.value.toLowerCase();
            const selectedTag = tagFilter.value;
            const pluginRows = document.querySelectorAll('#pluginsTable tbody tr');

            pluginRows.forEach(row => {
                const pluginName = row.querySelector('td:first-child').textContent.toLowerCase();
                const pluginDesc = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const pluginTags = row.getAttribute('data-plugin-tags') || '';
                const tagArray = pluginTags.split(',');

                // 检查是否匹配搜索词
                const matchesSearch = searchTerm === '' ||
                    pluginName.includes(searchTerm) ||
                    pluginDesc.includes(searchTerm);

                // 检查是否匹配选中的标签
                const matchesTag = selectedTag === '' || tagArray.includes(selectedTag);

                // 同时满足搜索和标签筛选条件才显示
                if (matchesSearch && matchesTag) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // 检查是否有可见行
            const visibleRows = document.querySelectorAll('#pluginsTable tbody tr:not([style*="display: none"])');
            const noResultsRow = document.getElementById('noResultsRow');

            // 如果没有可见行，显示"无匹配结果"
            if (visibleRows.length === 0) {
                if (!noResultsRow) {
                    const tbody = document.querySelector('#pluginsTable tbody');
                    const newRow = document.createElement('tr');
                    newRow.id = 'noResultsRow';
                    newRow.innerHTML = '<td colspan="5" class="px-3 py-4 text-center text-sm text-gray-500">没有匹配的插件</td>';
                    tbody.appendChild(newRow);
                } else {
                    noResultsRow.style.display = '';
                }
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        }
    }
});
</script>

</body>
</html>
