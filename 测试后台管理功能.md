# 后台管理功能测试指南

## 测试环境准备

### 1. 确保数据库中有测试数据
```sql
-- 插入测试应用数据
INSERT INTO app (appId, title, appCode, description, salerUserId, status, isOnline, isPrivate, createTime) VALUES
(1, '测试应用1', 'test-app-1', '这是一个测试应用', 1, 1, 0, 0, NOW()),
(2, '测试应用2', 'test-app-2', '这是另一个测试应用', 2, 1, 0, 0, NOW()),
(3, '已通过应用', 'approved-app', '已审核通过的应用', 1, 2, 1, 0, NOW()),
(4, '编辑中应用', 'draft-app', '正在编辑的应用', 3, 0, 0, 0, NOW());
```

### 2. 访问后台管理
打开浏览器访问：
```
http://your-domain/Saler/admin/index
```

## 功能测试清单

### ✅ 页面访问测试
- [ ] 后台首页能正常访问
- [ ] 页面布局正常显示
- [ ] 统计数据正确显示
- [ ] 搜索筛选表单正常

### ✅ 应用列表测试
- [ ] 应用列表正常显示
- [ ] 应用状态标签正确
- [ ] 分页功能正常
- [ ] 表格排序正常

### ✅ 搜索筛选测试
- [ ] 按应用名称搜索
- [ ] 按应用代码搜索
- [ ] 按审核状态筛选
- [ ] 按上线状态筛选
- [ ] 按开发者ID筛选
- [ ] 组合条件搜索

### ✅ 单个审核测试
- [ ] 审核通过功能
- [ ] 审核拒绝功能
- [ ] 拒绝理由输入
- [ ] 状态更新正确
- [ ] 成功提示显示

### ✅ 批量审核测试
- [ ] 全选/取消全选
- [ ] 选择计数正确
- [ ] 批量通过功能
- [ ] 批量拒绝功能
- [ ] 只能选择审核中的应用

### ✅ 状态管理测试
- [ ] 应用上线功能
- [ ] 应用下线功能
- [ ] 只有审核通过的应用能上线
- [ ] 状态更新正确

### ✅ 应用详情测试
- [ ] 详情页面正常显示
- [ ] 基本信息完整
- [ ] 状态显示正确
- [ ] 快速操作链接正常

### ✅ 权限验证测试
- [ ] 管理员中间件生效
- [ ] 非管理员无法访问
- [ ] 权限检查正确

## 测试步骤

### 1. 基础功能测试
```
1. 访问后台首页
2. 检查页面是否正常加载
3. 验证统计数据是否正确
4. 测试搜索和筛选功能
```

### 2. 审核功能测试
```
1. 找到状态为"审核中"的应用
2. 点击"通过"按钮，确认操作
3. 检查应用状态是否更新为"已通过"
4. 找到另一个"审核中"的应用
5. 点击"拒绝"按钮，输入拒绝理由
6. 检查应用状态是否更新为"编辑中"
```

### 3. 批量操作测试
```
1. 勾选多个"审核中"的应用
2. 点击"批量通过"按钮
3. 确认操作，检查状态更新
4. 测试"批量拒绝"功能
```

### 4. 状态管理测试
```
1. 找到"已通过"且"未上线"的应用
2. 点击"上线"按钮
3. 检查状态是否更新为"已上线"
4. 测试"下线"功能
```

## 常见问题排查

### 1. 页面无法访问
- 检查路由配置是否正确
- 确认控制器文件存在
- 检查中间件配置

### 2. 权限验证失败
- 检查AdminAuth中间件
- 确认管理员用户ID设置
- 验证权限检查逻辑

### 3. 数据不显示
- 检查数据库连接
- 确认模型关联正确
- 验证查询条件

### 4. 操作失败
- 检查表单提交数据
- 确认API接口正常
- 查看错误日志

### 5. 样式问题
- 确认Tailwind CSS加载
- 检查iconfont图标
- 验证CSS类名

## 性能测试

### 1. 大数据量测试
```sql
-- 插入大量测试数据
INSERT INTO app (title, appCode, description, salerUserId, status, isOnline, isPrivate, createTime)
SELECT 
    CONCAT('测试应用', id),
    CONCAT('test-app-', id),
    CONCAT('测试应用描述', id),
    FLOOR(RAND() * 10) + 1,
    FLOOR(RAND() * 3),
    FLOOR(RAND() * 2),
    FLOOR(RAND() * 2),
    NOW()
FROM (
    SELECT @row := @row + 1 as id
    FROM (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t1,
         (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t2,
         (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t3,
         (SELECT @row := 0) r
    LIMIT 1000
) numbers;
```

### 2. 分页性能测试
- 测试大数据量下的分页加载速度
- 验证搜索功能的响应时间
- 检查批量操作的处理效率

## 安全测试

### 1. 权限绕过测试
- 尝试直接访问后台URL
- 测试非管理员用户访问
- 验证API接口权限

### 2. 数据验证测试
- 测试恶意输入数据
- 验证参数校验
- 检查SQL注入防护

### 3. CSRF测试
- 测试跨站请求伪造
- 验证表单令牌
- 检查请求来源

## 测试报告模板

```
测试日期: ____
测试人员: ____
测试环境: ____

功能测试结果:
□ 页面访问 - 通过/失败
□ 应用列表 - 通过/失败
□ 搜索筛选 - 通过/失败
□ 单个审核 - 通过/失败
□ 批量审核 - 通过/失败
□ 状态管理 - 通过/失败
□ 应用详情 - 通过/失败
□ 权限验证 - 通过/失败

发现问题:
1. ____
2. ____
3. ____

建议改进:
1. ____
2. ____
3. ____
```
