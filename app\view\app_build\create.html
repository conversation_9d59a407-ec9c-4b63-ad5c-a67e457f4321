{extend name="common/base" /}

{block name="title"}{$app.title} - 创建应用包 - 应用市场管理系统{/block}

{block name="content"}
<div class="flex flex-col md:flex-row gap-6">
    <!-- 侧边栏 -->
    <div class="md:w-64 flex-shrink-0">
        {include file="common/sidebar" /}
    </div>

    <!-- 主内容 -->
    <div class="flex-1">
        <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="iconfont icon-add text-indigo-600 mr-2"></i>创建应用包
                </h2>
                <a href="{__MPRE__}{:url('appBuild/index', ['appId' => $app.appId])}" class="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-xs flex items-center">
                    <i class="iconfont icon-back mr-1"></i> 返回列表
                </a>
            </div>

            <form id="buildForm" data-validate>
                <!-- 基本信息 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-info-circle text-indigo-500 mr-2"></i>基本信息
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="versionCode" class="block text-xs font-medium text-gray-700 mb-1">版本号 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-version text-xs"></i>
                                </span>
                                <input type="number" id="versionCode" name="versionCode" required min="1"
                                    placeholder="输入版本号"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">必须大于上次发布的版本，添加后不可修改</p>
                        </div>

                        <div class="form-group">
                            <label for="versionName" class="block text-xs font-medium text-gray-700 mb-1">版本名称 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-tag text-xs"></i>
                                </span>
                                <input type="text" id="versionName" name="versionName" required maxlength="16"
                                    placeholder="如: 1.0.0"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div>

                        <div class="form-group md:col-span-2">
                            <label for="description" class="block text-xs font-medium text-gray-700 mb-1">说明 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <span class="absolute top-2 left-3 text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-description text-xs"></i>
                                </span>
                                <textarea id="description" name="description" rows="3" required maxlength="512"
                                    placeholder="请输入版本说明"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置信息 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-code text-indigo-500 mr-2"></i>配置信息
                    </h3>
                    <div class="grid grid-cols-1 gap-5">
                        <div class="form-group">
                            <label for="configSchema" class="block text-xs font-medium text-gray-700 mb-1">应用包级配置表单 (JSON)</label>
                            <div class="relative">
                                <div class="absolute top-1.5 left-3 text-gray-400 text-xs">{ }</div>
                                <textarea id="configSchema" name="configSchema" rows="3"
                                    placeholder="{}"
                                    class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                    focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                    hover:border-indigo-300 transition-colors duration-200 font-mono text-xs"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="appConfig" class="block text-xs font-medium text-gray-700 mb-1">应用配置 (JSON) <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute top-1.5 left-3 text-gray-400 text-xs">{ }</div>
                                <textarea id="appConfig" name="appConfig" rows="3" required
                                    class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                    focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                    hover:border-indigo-300 transition-colors duration-200 font-mono text-xs">{}</textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">类似微信小程序 app.json, {entryPagePath: "", ...}</p>
                        </div>

                        <div class="form-group">
                            <label for="pageConfig" class="block text-xs font-medium text-gray-700 mb-1">页面配置 (JSON)</label>
                            <div class="relative">
                                <div class="absolute top-1.5 left-3 text-gray-400 text-xs">[ ]</div>
                                <textarea id="pageConfig" name="pageConfig" rows="3"
                                    placeholder="[]"
                                    class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                    focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                    hover:border-indigo-300 transition-colors duration-200 font-mono text-xs"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">类似微信小程序 page 设置 [ { page: "pathRegexPattern", config: { } }, ... ]</p>
                        </div>
                    </div>
                </div>

                <!-- 状态设置 -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                        <i class="iconfont icon-setting text-indigo-500 mr-2"></i>状态设置
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="block text-xs font-medium text-gray-700 mb-2">是否有离线前端包 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-5 mt-1">
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="hasPackage" value="0" checked
                                            class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                            group-hover:border-indigo-400 peer-checked:border-indigo-600
                                            peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">无</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="hasPackage" value="1"
                                            class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                            group-hover:border-indigo-400 peer-checked:border-indigo-600
                                            peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">有</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="block text-xs font-medium text-gray-700 mb-2">前端离线包是否已上传 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-5 mt-1">
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="packageUploaded" value="0" checked
                                            class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                            group-hover:border-indigo-400 peer-checked:border-indigo-600
                                            peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">未上传</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer group">
                                    <div class="relative">
                                        <input type="radio" name="packageUploaded" value="1"
                                            class="peer sr-only">
                                        <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                            group-hover:border-indigo-400 peer-checked:border-indigo-600
                                            peer-checked:border-3 transition-all duration-200"></div>
                                    </div>
                                    <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">已上传</span>
                                </label>
                            </div>
                        </div>

                        <!-- 离线前端包上传区域 -->
                        <div id="packageUploadArea" class="form-group md:col-span-2" style="display: none;">
                            <label class="block text-xs font-medium text-gray-700 mb-2">
                                <i class="iconfont icon-upload text-indigo-500 mr-1"></i>离线前端包上传
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors duration-200">
                                <!-- 上传区域 -->
                                <div id="uploadDropZone" class="cursor-pointer">
                                    <div class="mb-4">
                                        <i class="iconfont icon-cloud-upload text-4xl text-gray-400"></i>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">点击选择文件或拖拽文件到此处</p>
                                    <p class="text-xs text-gray-500">支持 .zip, .tar.gz 格式，最大 50MB</p>
                                    <input type="file" id="packageFileInput" accept=".zip,.tar.gz,.tgz" class="hidden">
                                </div>

                                <!-- 上传进度 -->
                                <div id="uploadProgress" class="hidden">
                                    <div class="mb-4">
                                        <i class="iconfont icon-loading animate-spin text-2xl text-indigo-500"></i>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">正在上传...</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div id="progressBar" class="bg-indigo-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                    <p id="progressText" class="text-xs text-gray-500 mt-1">0%</p>
                                </div>

                                <!-- 上传成功 -->
                                <div id="uploadSuccess" class="hidden">
                                    <div class="mb-4">
                                        <i class="iconfont icon-check-circle text-2xl text-green-500"></i>
                                    </div>
                                    <p class="text-sm text-green-600 mb-2">上传成功</p>
                                    <p id="uploadedFileName" class="text-xs text-gray-500"></p>
                                    <button type="button" onclick="resetUpload()" class="mt-2 text-xs text-indigo-600 hover:text-indigo-800">重新上传</button>
                                </div>

                                <!-- 上传失败 -->
                                <div id="uploadError" class="hidden">
                                    <div class="mb-4">
                                        <i class="iconfont icon-close-circle text-2xl text-red-500"></i>
                                    </div>
                                    <p class="text-sm text-red-600 mb-2">上传失败</p>
                                    <p id="errorMessage" class="text-xs text-gray-500 mb-2"></p>
                                    <button type="button" onclick="resetUpload()" class="text-xs text-indigo-600 hover:text-indigo-800">重新上传</button>
                                </div>
                            </div>
                        </div>

                        <!-- <div class="form-group">
                            <label class="block text-xs font-medium text-gray-700 mb-1">状态 <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-status text-xs"></i>
                                </span>
                                <select name="status" required
                                    class="appearance-none w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200 bg-white">
                                    <option value="0">编辑中</option>
                                    <option value="1">审核中</option>
                                    <option value="2">审核通过</option>
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                                    <i class="iconfont icon-down text-xs"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="statusMessage" class="block text-xs font-medium text-gray-700 mb-1">状态信息</label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
                                    <i class="iconfont icon-message text-xs"></i>
                                </span>
                                <input type="text" id="statusMessage" name="statusMessage"
                                    placeholder="如拒绝理由等"
                                    class="w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                                    focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                                    hover:border-indigo-300 transition-colors duration-200">
                            </div>
                        </div> -->
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-100">
                    <button type="button" onclick="window.location.href='{__MPRE__}{:url(\'appBuild/index\', [\'appId\' => $app.appId])}'"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-close mr-1"></i> 取消
                    </button>
                    <button type="button" id="submitButton" onclick="submitForm()"
                        class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-save mr-1"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表单元素
    initFormElements();

    // 添加JSON格式化功能
    setupJsonFormatting();

    // 初始化上传功能
    initPackageUpload();
});

// 初始化表单元素
function initFormElements() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');

            // 简单验证（如果是必填字段）
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('border-red-500');

                // 添加错误提示
                const fieldGroup = this.closest('.form-group');
                if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                    const errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                    errorMsg.textContent = '此字段不能为空';
                    fieldGroup.appendChild(errorMsg);
                }
            } else {
                this.classList.remove('border-red-500');

                // 移除错误提示
                const fieldGroup = this.closest('.form-group');
                const errorMsg = fieldGroup?.querySelector('.error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    });
}

// 设置JSON格式化功能
function setupJsonFormatting() {
    const jsonFields = ['configSchema', 'appConfig', 'pageConfig'];

    jsonFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            // 添加格式化按钮
            const fieldGroup = field.closest('.form-group');
            const formatBtn = document.createElement('button');
            formatBtn.type = 'button';
            formatBtn.className = 'absolute top-1 right-1 bg-gray-100 text-gray-600 rounded px-1.5 py-0.5 text-xs hover:bg-gray-200 transition-colors duration-200';
            formatBtn.textContent = '格式化';
            formatBtn.onclick = function(e) {
                e.preventDefault();
                formatJsonField(fieldId);
            };

            const fieldContainer = field.closest('.relative');
            fieldContainer.appendChild(formatBtn);

            // 添加语法高亮
            field.addEventListener('input', function() {
                validateJsonField(this);
            });

            // 初始验证
            validateJsonField(field);
        }
    });
}

// 格式化JSON字段
function formatJsonField(fieldId) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    const value = field.value.trim();
    if (!value) return;

    try {
        const json = JSON.parse(value);
        field.value = JSON.stringify(json, null, 2);
        field.classList.remove('border-red-500');

        // 移除错误提示
        const fieldGroup = field.closest('.form-group');
        const errorMsg = fieldGroup?.querySelector('.error-message');
        if (errorMsg) {
            errorMsg.remove();
        }

        showNotification('JSON格式化成功', 'success');
    } catch (e) {
        field.classList.add('border-red-500');

        // 添加错误提示
        const fieldGroup = field.closest('.form-group');
        if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
            const errorMsg = document.createElement('p');
            errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
            errorMsg.textContent = 'JSON格式无效: ' + e.message;
            fieldGroup.appendChild(errorMsg);
        }

        showNotification('JSON格式无效，请检查', 'error');
    }
}

// 验证JSON字段
function validateJsonField(field) {
    if (!field) return true;

    const value = field.value.trim();
    if (!value) return true;

    try {
        JSON.parse(value);
        field.classList.remove('border-red-500');

        // 移除错误提示
        const fieldGroup = field.closest('.form-group');
        const errorMsg = fieldGroup?.querySelector('.error-message');
        if (errorMsg) {
            errorMsg.remove();
        }

        return true;
    } catch (e) {
        field.classList.add('border-red-500');
        return false;
    }
}

// 验证表单
function validateForm() {
    const form = document.getElementById('buildForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    // 验证必填字段
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('border-red-500');

            // 添加抖动效果
            field.classList.add('animate-shake');
            setTimeout(() => {
                field.classList.remove('animate-shake');
            }, 600);

            // 添加错误提示
            const fieldGroup = field.closest('.form-group');
            if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                const errorMsg = document.createElement('p');
                errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                errorMsg.textContent = '此字段不能为空';
                fieldGroup.appendChild(errorMsg);
            }

            isValid = false;
        }
    });

    // 验证JSON字段
    const jsonFields = ['configSchema', 'appConfig', 'pageConfig'];
    jsonFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            const value = field.value.trim();

            // 如果是必填字段或有值，则验证JSON格式
            if (field.hasAttribute('required') || value) {
                if (value && !validateJsonField(field)) {
                    // 添加错误提示
                    const fieldGroup = field.closest('.form-group');
                    if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                        const errorMsg = document.createElement('p');
                        errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                        errorMsg.textContent = 'JSON格式无效';
                        fieldGroup.appendChild(errorMsg);
                    }

                    isValid = false;
                }
            }
        }
    });

    // 如果有错误，滚动到第一个错误字段
    if (!isValid) {
        const firstErrorField = form.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorField.focus();
        }
    }

    return isValid;
}

// 提交表单
async function submitForm() {
    // 验证表单
    if (!validateForm()) {
        showNotification('表单验证失败，请检查输入', 'error');
        return;
    }

    // 显示加载状态
    const submitButton = document.getElementById('submitButton');
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="iconfont icon-loading animate-spin mr-1"></i> 保存中...';
    submitButton.disabled = true;

    try {
        const form = document.getElementById('buildForm');
        const formData = new FormData(form);

        // 发送请求
        const response = await fetch('{__MPRE__}{:url("appBuild/save")}?appId={$app.appId}', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.code === 1) {
            // 如果有上传的包，需要关联到真实的应用包ID
            if (window.tempAppBuildId && data.data && data.data.appBuildId) {
                try {
                    await linkUploadedPackage(data.data.appBuildId);
                } catch (error) {
                    console.warn('关联上传包失败:', error);
                    // 不影响主流程，只是警告
                }
            }

            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.href = '{__MPRE__}{:url("appBuild/index", ["appId" => $app.appId])}';
            }, 1000);
        } else {
            showNotification(data.msg || '保存失败', 'error');
            resetButton();
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('保存失败，请重试', 'error');
        resetButton();
    }

    function resetButton() {
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 初始化上传功能
function initPackageUpload() {
    // 监听"是否有离线前端包"选项变化
    const hasPackageRadios = document.querySelectorAll('input[name="hasPackage"]');
    hasPackageRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            togglePackageUploadArea();
        });
    });

    // 初始化文件上传
    setupFileUpload();

    // 初始状态检查
    togglePackageUploadArea();
}

// 切换上传区域显示/隐藏
function togglePackageUploadArea() {
    const hasPackage = document.querySelector('input[name="hasPackage"]:checked').value;
    const uploadArea = document.getElementById('packageUploadArea');

    if (hasPackage === '1') {
        uploadArea.style.display = 'block';
    } else {
        uploadArea.style.display = 'none';
        // 重置上传状态
        resetUpload();
    }
}

// 设置文件上传
function setupFileUpload() {
    const dropZone = document.getElementById('uploadDropZone');
    const fileInput = document.getElementById('packageFileInput');

    // 点击上传区域
    dropZone.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileUpload(file);
        }
    });

    // 拖拽上传
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-indigo-500', 'bg-indigo-50');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-indigo-500', 'bg-indigo-50');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-indigo-500', 'bg-indigo-50');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });
}

// 处理文件上传
async function handleFileUpload(file) {
    // 验证文件
    if (!validateFile(file)) {
        return;
    }

    try {
        // 显示上传进度
        showUploadProgress();

        // 1. 获取上传参数
        const uploadPolicy = await getUploadPolicy();

        // 2. 上传文件到CDN
        const uploadResult = await uploadToCDN(file, uploadPolicy);

        // 3. 通知后台完成
        await notifyUploadCompleted(uploadResult.key);

        // 显示上传成功
        showUploadSuccess(file.name);

        // 自动更新"已上传"状态
        document.querySelector('input[name="packageUploaded"][value="1"]').checked = true;

    } catch (error) {
        console.error('Upload error:', error);
        showUploadError(error.message);
    }
}

// 验证文件
function validateFile(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = ['.zip', '.tar.gz', '.tgz'];

    // 检查文件大小
    if (file.size > maxSize) {
        showUploadError('文件大小不能超过50MB');
        return false;
    }

    // 检查文件类型
    const fileName = file.name.toLowerCase();
    const isValidType = allowedTypes.some(type => fileName.endsWith(type));

    if (!isValidType) {
        showUploadError('只支持 .zip, .tar.gz 格式的文件');
        return false;
    }

    return true;
}

// 获取上传参数
async function getUploadPolicy() {
    // 使用应用ID和时间戳生成临时ID
    const appId = '{$app.appId}';
    const tempAppBuildId = `temp_${appId}_${Date.now()}`;

    const response = await fetch(`/developer/xapi/market/?want=uploadPackagePolicy&appBuildId=${tempAppBuildId}`, {
        method: 'GET'
    });

    const data = await response.json();

    if (data.e !== 0) {
        throw new Error(data.m || '获取上传参数失败');
    }

    // 保存临时ID供后续使用
    window.tempAppBuildId = tempAppBuildId;

    return data.d;
}

// 上传文件到CDN
async function uploadToCDN(file, policy) {
    const formData = new FormData();

    // 添加CDN上传所需的参数
    Object.keys(policy.formData).forEach(key => {
        formData.append(key, policy.formData[key]);
    });

    // 添加文件
    formData.append('file', file);

    const xhr = new XMLHttpRequest();

    return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = Math.round((e.loaded / e.total) * 100);
                updateUploadProgress(percentComplete);
            }
        });

        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve({
                        key: policy.key,
                        url: policy.url
                    });
                } catch (e) {
                    // 某些CDN可能返回非JSON格式
                    resolve({
                        key: policy.key,
                        url: policy.url
                    });
                }
            } else {
                reject(new Error('上传失败'));
            }
        });

        xhr.addEventListener('error', function() {
            reject(new Error('网络错误'));
        });

        xhr.open('POST', policy.uploadUrl);
        xhr.send(formData);
    });
}

// 通知后台上传完成
async function notifyUploadCompleted(key) {
    const tempAppBuildId = window.tempAppBuildId; // 使用之前保存的临时ID

    const response = await fetch(`/developer/xapi/market/?want=uploadPackageCompleted&appBuildId=${tempAppBuildId}&key=${key}`, {
        method: 'POST'
    });

    const data = await response.json();

    if (data.e !== 0) {
        throw new Error(data.m || '通知后台失败');
    }

    return data.d;
}

// 关联上传的包到真实的应用包ID
async function linkUploadedPackage(realAppBuildId) {
    const tempAppBuildId = window.tempAppBuildId;

    const response = await fetch(`/developer/xapi/market/?want=linkUploadedPackage&tempAppBuildId=${tempAppBuildId}&appBuildId=${realAppBuildId}`, {
        method: 'POST'
    });

    const data = await response.json();

    if (data.e !== 0) {
        throw new Error(data.m || '关联上传包失败');
    }

    return data.d;
}

// 显示上传进度
function showUploadProgress() {
    document.getElementById('uploadDropZone').style.display = 'none';
    hideAllUploadStates();
    document.getElementById('uploadProgress').classList.remove('hidden');
}

// 更新上传进度
function updateUploadProgress(percent) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    progressBar.style.width = percent + '%';
    progressText.textContent = percent + '%';
}

// 显示上传成功
function showUploadSuccess(fileName) {
    document.getElementById('uploadDropZone').style.display = 'none';
    hideAllUploadStates();
    document.getElementById('uploadSuccess').classList.remove('hidden');
    document.getElementById('uploadedFileName').textContent = fileName;
}

// 显示上传错误
function showUploadError(message) {
    document.getElementById('uploadDropZone').style.display = 'none';
    hideAllUploadStates();
    document.getElementById('uploadError').classList.remove('hidden');
    document.getElementById('errorMessage').textContent = message;
}

// 重置上传状态
function resetUpload() {
    hideAllUploadStates();
    document.getElementById('uploadDropZone').style.display = 'block';
    document.getElementById('packageFileInput').value = '';

    // 重置进度
    updateUploadProgress(0);

    // 重置上传状态
    document.querySelector('input[name="packageUploaded"][value="0"]').checked = true;
}

// 隐藏所有上传状态
function hideAllUploadStates() {
    document.getElementById('uploadProgress').classList.add('hidden');
    document.getElementById('uploadSuccess').classList.add('hidden');
    document.getElementById('uploadError').classList.add('hidden');
}
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 错误提示动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
    animation: shake 0.6s ease-in-out;
}

/* 输入框焦点效果 */
.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select, textarea {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}
</style>
{/block}
