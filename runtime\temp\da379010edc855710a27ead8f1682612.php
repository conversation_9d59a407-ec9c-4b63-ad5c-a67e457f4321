<?php /*a:4:{s:43:"E:\Ym\marketAdmin\app\view\admin\index.html";i:1748593784;s:43:"E:\Ym\marketAdmin\app\view\common\base.html";i:1747638572;s:45:"E:\Ym\marketAdmin\app\view\common\header.html";i:1747708602;s:45:"E:\Ym\marketAdmin\app\view\common\footer.html";i:1747638354;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>后台管理 - 应用审核 - 应用市场管理系统</title>
    <script src="/static/js/tw.js"></script>
    <link rel="stylesheet" href="/static/css/iconfont.css">
    <style>
        /* 基础响应式样式 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }
        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表格响应式样式 */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .touch-action-manipulation {
                touch-action: manipulation;
            }
            .tap-highlight-transparent {
                -webkit-tap-highlight-color: transparent;
            }
        }
    </style>
    
</head>
<body class="bg-gray-100 min-h-screen touch-action-manipulation tap-highlight-transparent">
    <!-- 移动端菜单按钮 -->
    <div id="mobile-menu-button" class="fixed z-50 bottom-4 right-4 md:hidden bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
        <i class="iconfont icon-menu text-xl"></i>
    </div>

    <!-- 头部 -->
    

    <!-- 主体内容 -->
    <div class="container mx-auto px-4 py-6">
        
<div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="iconfont icon-admin text-indigo-600 mr-2"></i>
                        后台管理
                    </h1>
                    <span class="ml-4 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">管理员</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<?php echo url('app/index'); ?>" class="text-gray-600 hover:text-indigo-600 text-sm">
                        <i class="iconfont icon-back mr-1"></i>返回前台
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-app text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">总应用数</p>
                        <p class="text-2xl font-semibold text-gray-900"><?php echo htmlentities((string) $stats['total']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-clock text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">待审核</p>
                        <p class="text-2xl font-semibold text-yellow-600"><?php echo htmlentities((string) $stats['pending']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-check-circle text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已通过</p>
                        <p class="text-2xl font-semibold text-green-600"><?php echo htmlentities((string) $stats['approved']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-edit text-2xl text-gray-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">编辑中</p>
                        <p class="text-2xl font-semibold text-gray-600"><?php echo htmlentities((string) $stats['draft']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-online text-2xl text-indigo-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已上线</p>
                        <p class="text-2xl font-semibold text-indigo-600"><?php echo htmlentities((string) $stats['online']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">搜索筛选</h3>
                <form method="GET" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">应用名称</label>
                        <input type="text" name="title" value="<?php echo htmlentities((string) $search['title']); ?>" placeholder="搜索应用名称"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">应用代码</label>
                        <input type="text" name="appCode" value="<?php echo htmlentities((string) $search['appCode']); ?>" placeholder="搜索应用代码"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核状态</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">全部状态</option>
                            <option value="0" <?php if($search['status'] == '0'): ?>selected<?php endif; ?>>编辑中</option>
                            <option value="1" <?php if($search['status'] == '1'): ?>selected<?php endif; ?>>审核中</option>
                            <option value="2" <?php if($search['status'] == '2'): ?>selected<?php endif; ?>>审核通过</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">上线状态</label>
                        <select name="isOnline" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">全部</option>
                            <option value="1" <?php if($search['isOnline'] == '1'): ?>selected<?php endif; ?>>已上线</option>
                            <option value="0" <?php if($search['isOnline'] == '0'): ?>selected<?php endif; ?>>未上线</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">开发者ID</label>
                        <input type="number" name="salerUserId" value="<?php echo htmlentities((string) $search['salerUserId']); ?>" placeholder="开发者ID"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="iconfont icon-search mr-1"></i>搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">全选</span>
                        </label>
                        <span id="selectedCount" class="text-sm text-gray-500">已选择 0 项</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="batchReview('approve')" class="px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:opacity-50" disabled id="batchApproveBtn">
                            <i class="iconfont icon-check mr-1"></i>批量通过
                        </button>
                        <button type="button" onclick="batchReview('reject')" class="px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:opacity-50" disabled id="batchRejectBtn">
                            <i class="iconfont icon-close mr-1"></i>批量拒绝
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应用列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开发者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$app): $mod = ($i % 2 );++$i;?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="app-checkbox rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" value="<?php echo htmlentities((string) $app['appId']); ?>" <?php if($app['status'] != 1): ?>disabled<?php endif; ?>>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <?php if($app['logo']): ?>
                                        <img class="h-12 w-12 rounded-lg object-cover" src="<?php echo htmlentities((string) $app['logo']); ?>" alt="<?php echo htmlentities((string) $app['title']); ?>">
                                        <?php else: ?>
                                        <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                            <i class="iconfont icon-app text-gray-400 text-xl"></i>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlentities((string) $app['title']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlentities((string) $app['appCode']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                用户ID: <?php echo htmlentities((string) $app['salerUserId']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-col space-y-1">
                                    <!-- 审核状态 -->
                                    <?php if($app['status'] == 0): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="iconfont icon-edit mr-1"></i>编辑中
                                    </span>
                                    <?php elseif($app['status'] == 1): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        <i class="iconfont icon-clock mr-1"></i>审核中
                                    </span>
                                    <?php else: ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="iconfont icon-check-circle mr-1"></i>已通过
                                    </span>
                                    <?php endif; ?>

                                    <!-- 上线状态 -->
                                    <?php if($app['isOnline']): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                        <i class="iconfont icon-online mr-1"></i>已上线
                                    </span>
                                    <?php else: ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <i class="iconfont icon-offline mr-1"></i>未上线
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($app['createTime'])? strtotime($app['createTime']) : $app['createTime'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="<?php echo url('admin/detail'); ?>?appId=<?php echo htmlentities((string) $app['appId']); ?>" class="text-indigo-600 hover:text-indigo-900">
                                    <i class="iconfont icon-eye mr-1"></i>查看
                                </a>

                                <?php if($app['status'] == 1): ?>
                                <button type="button" onclick="reviewApp(<?php echo htmlentities((string) $app['appId']); ?>, 'approve')" class="text-green-600 hover:text-green-900">
                                    <i class="iconfont icon-check mr-1"></i>通过
                                </button>
                                <button type="button" onclick="reviewApp(<?php echo htmlentities((string) $app['appId']); ?>, 'reject')" class="text-red-600 hover:text-red-900">
                                    <i class="iconfont icon-close mr-1"></i>拒绝
                                </button>
                                <?php endif; if($app['status'] == 2): ?>
                                <button type="button" onclick="updateStatus(<?php echo htmlentities((string) $app['appId']); ?>, <?php echo !empty($app['isOnline']) ? 0  :  1; ?>)" class="text-blue-600 hover:text-blue-900">
                                    <i class="iconfont icon-<?php echo !empty($app['isOnline']) ? 'offline'  :  'online'; ?> mr-1"></i><?php echo !empty($app['isOnline']) ? '下线'  :  '上线'; ?>
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <?php echo htmlentities((string) $list->render()); ?>
            </div>
        </div>
    </div>
</div>

    </div>

    <!-- 底部 -->
    <footer class="bg-white shadow mt-8 py-4">
    <div class="container mx-auto px-4">
        <div class="text-center text-gray-500 text-sm">
            &copy; 2025 应用市场管理系统 
        </div>
    </div>
</footer>


    <script src="/static/js/app.js"></script>
    
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initCheckboxes();
});

// 初始化复选框功能
function initCheckboxes() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const appCheckboxes = document.querySelectorAll('.app-checkbox:not([disabled])');
    const selectedCountSpan = document.getElementById('selectedCount');
    const batchApproveBtn = document.getElementById('batchApproveBtn');
    const batchRejectBtn = document.getElementById('batchRejectBtn');

    // 全选/取消全选
    selectAllCheckbox.addEventListener('change', function() {
        appCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // 单个复选框变化
    appCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('.app-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === appCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < appCheckboxes.length;
        });
    });

    // 更新选中数量和按钮状态
    function updateSelectedCount() {
        const checkedBoxes = document.querySelectorAll('.app-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountSpan.textContent = `已选择 ${count} 项`;

        // 更新批量操作按钮状态
        batchApproveBtn.disabled = count === 0;
        batchRejectBtn.disabled = count === 0;
    }
}

// 审核单个应用
async function reviewApp(appId, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';

    // 如果是拒绝，询问拒绝理由
    let message = '';
    if (action === 'reject') {
        message = prompt('请输入拒绝理由（可选）：');
        if (message === null) return; // 用户取消
    }

    if (!confirm(`确定要${actionText}这个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('<?php echo url("admin/review"); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appId: appId,
                action: action,
                message: message
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 批量审核
async function batchReview(action) {
    const checkedBoxes = document.querySelectorAll('.app-checkbox:checked');
    const appIds = Array.from(checkedBoxes).map(cb => cb.value);

    if (appIds.length === 0) {
        showNotification('请选择要审核的应用', 'warning');
        return;
    }

    const actionText = action === 'approve' ? '通过' : '拒绝';

    // 如果是拒绝，询问拒绝理由
    let message = '';
    if (action === 'reject') {
        message = prompt('请输入拒绝理由（可选）：');
        if (message === null) return; // 用户取消
    }

    if (!confirm(`确定要批量${actionText} ${appIds.length} 个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('<?php echo url("admin/batchReview"); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appIds: appIds,
                action: action,
                message: message
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 更新应用状态（上线/下线）
async function updateStatus(appId, isOnline) {
    const statusText = isOnline ? '上线' : '下线';

    if (!confirm(`确定要${statusText}这个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('<?php echo url("admin/updateStatus"); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appId: appId,
                isOnline: isOnline
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        type === 'warning' ? 'icon-warning' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 表格悬停效果 */
tbody tr:hover {
    background-color: #f9fafb;
}

/* 复选框样式优化 */
input[type="checkbox"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>

</body>
</html>
