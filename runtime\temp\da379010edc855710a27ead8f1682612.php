<?php /*a:4:{s:43:"E:\Ym\marketAdmin\app\view\admin\index.html";i:1748595315;s:43:"E:\Ym\marketAdmin\app\view\common\base.html";i:1747638572;s:45:"E:\Ym\marketAdmin\app\view\common\header.html";i:1747708602;s:45:"E:\Ym\marketAdmin\app\view\common\footer.html";i:1747638354;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>后台管理概览 - 应用市场管理系统</title>
    <script src="/static/js/tw.js"></script>
    <link rel="stylesheet" href="/static/css/iconfont.css">
    <style>
        /* 基础响应式样式 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }
        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表格响应式样式 */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .touch-action-manipulation {
                touch-action: manipulation;
            }
            .tap-highlight-transparent {
                -webkit-tap-highlight-color: transparent;
            }
        }
    </style>
    
</head>
<body class="bg-gray-100 min-h-screen touch-action-manipulation tap-highlight-transparent">
    <!-- 移动端菜单按钮 -->
    <div id="mobile-menu-button" class="fixed z-50 bottom-4 right-4 md:hidden bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
        <i class="iconfont icon-menu text-xl"></i>
    </div>

    <!-- 头部 -->
    

    <!-- 主体内容 -->
    <div class="container mx-auto px-4 py-6">
        
<div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="iconfont icon-admin text-indigo-600 mr-2"></i>
                        后台管理概览
                    </h1>
                    <span class="ml-4 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">管理员</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<?php echo url('admin/reviewCenter'); ?>" class="px-4 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors duration-200 flex items-center">
                        <i class="iconfont icon-review mr-2"></i>进入审核中心
                        <?php if($stats['pending'] > 0): ?>
                        <span class="ml-2 px-2 py-1 bg-orange-800 text-xs rounded-full"><?php echo htmlentities((string) $stats['pending']); ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="<?php echo url('app/index'); ?>" class="text-gray-600 hover:text-indigo-600 text-sm">
                        <i class="iconfont icon-back mr-1"></i>返回前台
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 欢迎信息 -->
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-8 mb-8 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-3xl font-bold mb-2">欢迎来到后台管理</h2>
                    <p class="text-indigo-100 text-lg">统一管理应用审核、应用包审核和运行环境审核</p>
                </div>
                <div class="hidden md:block">
                    <i class="iconfont icon-admin text-6xl text-indigo-200"></i>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-app text-3xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">应用总数</p>
                        <p class="text-3xl font-bold text-gray-900"><?php echo htmlentities((string) $stats['total']); ?></p>
                        <p class="text-xs text-gray-500 mt-1">所有应用</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-orange-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-clock text-3xl text-orange-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">待审核</p>
                        <p class="text-3xl font-bold text-orange-600"><?php echo htmlentities((string) $stats['pending']); ?></p>
                        <p class="text-xs text-gray-500 mt-1">需要处理</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-check-circle text-3xl text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">已通过</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo htmlentities((string) $stats['approved']); ?></p>
                        <p class="text-xs text-gray-500 mt-1">审核通过</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-indigo-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="iconfont icon-online text-3xl text-indigo-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">已上线</p>
                        <p class="text-3xl font-bold text-indigo-600"><?php echo htmlentities((string) $stats['online']); ?></p>
                        <p class="text-xs text-gray-500 mt-1">正在运行</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- 审核中心 -->
            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="iconfont icon-review text-2xl text-orange-600"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-semibold text-gray-900">审核中心</h3>
                            <p class="text-sm text-gray-500">统一审核管理</p>
                        </div>
                    </div>
                    <?php if($stats['pending'] > 0): ?>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        <?php echo htmlentities((string) $stats['pending']); ?> 待处理
                    </span>
                    <?php endif; ?>
                </div>
                <p class="text-gray-600 text-sm mb-4">管理所有应用、应用包和运行环境的审核</p>
                <a href="<?php echo url('admin/reviewCenter'); ?>" class="inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors duration-200">
                    <i class="iconfont icon-arrow-right mr-1"></i>进入审核中心
                </a>
            </div>

            <!-- 应用管理 -->
            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="iconfont icon-app text-2xl text-blue-600"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-semibold text-gray-900">应用管理</h3>
                            <p class="text-sm text-gray-500">管理所有用户应用</p>
                        </div>
                    </div>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <?php echo htmlentities((string) $stats['total']); ?> 个应用
                    </span>
                </div>
                <p class="text-gray-600 text-sm mb-4">查看和管理所有用户的应用信息</p>
                <a href="<?php echo url('admin/appList'); ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200">
                    <i class="iconfont icon-arrow-right mr-1"></i>管理应用列表
                </a>
            </div>

            <!-- 系统设置 -->
            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="iconfont icon-setting text-2xl text-gray-600"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-semibold text-gray-900">系统设置</h3>
                            <p class="text-sm text-gray-500">配置管理</p>
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 text-sm mb-4">系统配置和参数设置</p>
                <button class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 transition-colors duration-200" disabled>
                    <i class="iconfont icon-arrow-right mr-1"></i>敬请期待
                </button>
            </div>
        </div>

        <!-- 待处理事项和最近活动 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 待处理事项 -->
            <div class="bg-white rounded-lg shadow-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="iconfont icon-clock text-orange-600 mr-2"></i>
                        待处理事项
                        <?php if($stats['pending'] > 0): ?>
                        <span class="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full"><?php echo htmlentities((string) $stats['pending']); ?></span>
                        <?php endif; ?>
                    </h3>
                </div>
                <div class="p-6">
                    <?php if($stats['pending'] > 0): ?>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-orange-50 rounded-lg border border-orange-200">
                            <div class="flex items-center">
                                <i class="iconfont icon-review text-orange-600 text-xl mr-3"></i>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">有 <?php echo htmlentities((string) $stats['pending']); ?> 项内容等待审核</p>
                                    <p class="text-xs text-gray-500">包括应用、应用包和运行环境</p>
                                </div>
                            </div>
                            <a href="<?php echo url('admin/reviewCenter'); ?>" class="px-3 py-1.5 bg-orange-600 text-white text-xs rounded-md hover:bg-orange-700 transition-colors duration-200">
                                立即处理
                            </a>
                        </div>

                        <div class="text-center py-4">
                            <p class="text-sm text-gray-500 mb-3">快速进入审核中心处理待审核项目</p>
                            <a href="<?php echo url('admin/reviewCenter'); ?>" class="inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors duration-200">
                                <i class="iconfont icon-review mr-2"></i>进入审核中心
                            </a>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-8">
                        <i class="iconfont icon-check-circle text-4xl text-green-500 mb-3"></i>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">太棒了！</h4>
                        <p class="text-gray-500">当前没有待处理的审核事项</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 系统概览 -->
            <div class="bg-white rounded-lg shadow-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="iconfont icon-chart text-indigo-600 mr-2"></i>
                        系统概览
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- 应用统计 -->
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="iconfont icon-app text-blue-600 text-lg mr-3"></i>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">应用总数</p>
                                    <p class="text-xs text-gray-500">已注册的应用数量</p>
                                </div>
                            </div>
                            <span class="text-2xl font-bold text-blue-600"><?php echo htmlentities((string) $stats['total']); ?></span>
                        </div>

                        <!-- 审核通过率 -->
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="iconfont icon-check-circle text-green-600 text-lg mr-3"></i>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">审核通过</p>
                                    <p class="text-xs text-gray-500">已通过审核的应用</p>
                                </div>
                            </div>
                            <span class="text-2xl font-bold text-green-600"><?php echo htmlentities((string) $stats['approved']); ?></span>
                        </div>

                        <!-- 在线应用 -->
                        <div class="flex items-center justify-between p-3 bg-indigo-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="iconfont icon-online text-indigo-600 text-lg mr-3"></i>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">在线应用</p>
                                    <p class="text-xs text-gray-500">正在运行的应用</p>
                                </div>
                            </div>
                            <span class="text-2xl font-bold text-indigo-600"><?php echo htmlentities((string) $stats['online']); ?></span>
                        </div>

                        <div class="pt-4 border-t border-gray-200">
                            <a href="<?php echo url('app/index'); ?>" class="block w-full text-center px-4 py-2 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 transition-colors duration-200">
                                查看详细应用列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <!-- 底部 -->
    <footer class="bg-white shadow mt-8 py-4">
    <div class="container mx-auto px-4">
        <div class="text-center text-gray-500 text-sm">
            &copy; 2025 应用市场管理系统 
        </div>
    </div>
</footer>


    <script src="/static/js/app.js"></script>
    
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加卡片悬停效果
    const cards = document.querySelectorAll('.hover\\:shadow-xl');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 检查是否有待处理事项，添加提醒动画
    const pendingCount = <?php echo htmlentities((string) $stats['pending']); ?>;
    if (pendingCount > 0) {
        const reviewCenterCards = document.querySelectorAll('a[href*="reviewCenter"]');
        reviewCenterCards.forEach(card => {
            card.classList.add('animate-pulse-slow');
        });
    }
});

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        type === 'warning' ? 'icon-warning' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 渐变背景 */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* 卡片悬停效果 */
.hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease-in-out;
}

/* 慢速脉冲动画 */
@keyframes pulse-slow {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.animate-pulse-slow {
    animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 按钮悬停效果 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* 卡片边框效果 */
.border-l-4 {
    border-left-width: 4px;
}

/* 统计卡片动画 */
.stats-card {
    transform: translateY(0);
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

/* 快捷操作卡片 */
.action-card {
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.action-card:hover::before {
    left: 100%;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .px-8 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .py-8 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }

    .text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }

    .text-6xl {
        font-size: 3rem;
        line-height: 1;
    }
}

/* 阴影效果 */
.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 禁用状态 */
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 焦点效果 */
button:focus,
a:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
}
</style>

</body>
</html>
