{extend name="common/base" /}

{block name="title"}{$app.title} - 应用详情 - 后台管理 - 应用市场管理系统{/block}

{block name="content"}
<div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="{__MPRE__}{:url('admin/index')}" class="text-gray-600 hover:text-indigo-600 mr-4">
                        <i class="iconfont icon-back text-xl"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900">应用详情</h1>
                </div>
                <div class="flex items-center space-x-4">
                    {if $app.status == 1}
                    <button type="button" onclick="reviewApp({$app.appId}, 'approve')" class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700">
                        <i class="iconfont icon-check mr-1"></i>审核通过
                    </button>
                    <button type="button" onclick="reviewApp({$app.appId}, 'reject')" class="px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700">
                        <i class="iconfont icon-close mr-1"></i>审核拒绝
                    </button>
                    {/if}
                    
                    {if $app.status == 2}
                    <button type="button" onclick="updateStatus({$app.appId}, {$app.isOnline ? 0 : 1})" class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                        <i class="iconfont icon-{$app.isOnline ? 'offline' : 'online'} mr-1"></i>{$app.isOnline ? '下线应用' : '上线应用'}
                    </button>
                    {/if}
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 左侧：应用基本信息 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 基本信息卡片 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-start space-x-6">
                        <div class="flex-shrink-0">
                            {if $app.logo}
                            <img class="h-24 w-24 rounded-lg object-cover" src="{$app.logo}" alt="{$app.title}">
                            {else}
                            <div class="h-24 w-24 rounded-lg bg-gray-200 flex items-center justify-center">
                                <i class="iconfont icon-app text-gray-400 text-3xl"></i>
                            </div>
                            {/if}
                        </div>
                        <div class="flex-1">
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">{$app.title}</h2>
                            <p class="text-gray-600 mb-4">{$app.description}</p>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">应用代码:</span>
                                    <span class="font-medium text-gray-900">{$app.appCode}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">开发者ID:</span>
                                    <span class="font-medium text-gray-900">{$app.salerUserId}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">创建时间:</span>
                                    <span class="font-medium text-gray-900">{$app.createTime|date='Y-m-d H:i:s'}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">应用类型:</span>
                                    <span class="font-medium text-gray-900">{$app.isPrivate ? '私有' : '公开'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细描述 -->
                {if $app.detail}
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">详细描述</h3>
                    <div class="prose max-w-none text-gray-700">
                        {$app.detail|raw}
                    </div>
                </div>
                {/if}

                <!-- 配置信息 -->
                {if $app.configSchema}
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">配置表单</h3>
                    <pre class="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code>{$app.configSchema|json_encode:JSON_PRETTY_PRINT}</code></pre>
                </div>
                {/if}

                <!-- 审核信息 -->
                {if $app.statusMessage}
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">审核信息</h3>
                    <div class="bg-gray-50 p-4 rounded-md">
                        <p class="text-gray-700">{$app.statusMessage}</p>
                    </div>
                </div>
                {/if}
            </div>

            <!-- 右侧：状态和统计信息 -->
            <div class="space-y-6">
                <!-- 状态卡片 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">应用状态</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">审核状态</span>
                            <div>
                                {if $app.status == 0}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800">
                                    <i class="iconfont icon-edit mr-1"></i>编辑中
                                </span>
                                {elseif $app.status == 1}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    <i class="iconfont icon-clock mr-1"></i>审核中
                                </span>
                                {else}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
                                    <i class="iconfont icon-check-circle mr-1"></i>已通过
                                </span>
                                {/if}
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">上线状态</span>
                            <div>
                                {if $app.isOnline}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                    <i class="iconfont icon-online mr-1"></i>已上线
                                </span>
                                {else}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800">
                                    <i class="iconfont icon-offline mr-1"></i>未上线
                                </span>
                                {/if}
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">应用类型</span>
                            <div>
                                {if $app.isPrivate}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">
                                    <i class="iconfont icon-lock mr-1"></i>私有
                                </span>
                                {else}
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <i class="iconfont icon-unlock mr-1"></i>公开
                                </span>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">统计信息</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">运行环境数量</span>
                            <span class="font-semibold text-gray-900">{$runtimeCount}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">应用包数量</span>
                            <span class="font-semibold text-gray-900">{$buildCount}</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <a href="{__MPRE__}{:url('app/detail')}?appId={$app.appId}" target="_blank" class="block w-full px-4 py-2 text-center text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                            <i class="iconfont icon-eye mr-1"></i>查看前台详情
                        </a>
                        <a href="{__MPRE__}{:url('appBuild/index')}?appId={$app.appId}" target="_blank" class="block w-full px-4 py-2 text-center text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                            <i class="iconfont icon-package mr-1"></i>查看应用包
                        </a>
                        {if $runtimeCount > 0}
                        <a href="{__MPRE__}{:url('app/detail')}?appId={$app.appId}#runtimes" target="_blank" class="block w-full px-4 py-2 text-center text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                            <i class="iconfont icon-runtime mr-1"></i>查看运行环境
                        </a>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 审核应用
async function reviewApp(appId, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';
    
    // 如果是拒绝，询问拒绝理由
    let message = '';
    if (action === 'reject') {
        message = prompt('请输入拒绝理由（可选）：');
        if (message === null) return; // 用户取消
    }
    
    if (!confirm(`确定要${actionText}这个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('{__MPRE__}{:url("admin/review")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appId: appId,
                action: action,
                message: message
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 更新应用状态（上线/下线）
async function updateStatus(appId, isOnline) {
    const statusText = isOnline ? '上线' : '下线';
    
    if (!confirm(`确定要${statusText}这个应用吗？`)) {
        return;
    }

    try {
        const response = await fetch('{__MPRE__}{:url("admin/updateStatus")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                appId: appId,
                isOnline: isOnline
            })
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.msg || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('操作失败，请重试', 'error');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        type === 'warning' ? 'icon-warning' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 代码块样式 */
pre code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}
</style>
{/block}
