<?php /*a:4:{s:41:"E:\Ym\marketAdmin\app\view\app\index.html";i:1748593952;s:43:"E:\Ym\marketAdmin\app\view\common\base.html";i:1747638572;s:45:"E:\Ym\marketAdmin\app\view\common\header.html";i:1747708602;s:45:"E:\Ym\marketAdmin\app\view\common\footer.html";i:1747638354;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>应用列表 - 应用市场管理系统</title>
    <script src="/static/js/tw.js"></script>
    <link rel="stylesheet" href="/static/css/iconfont.css">
    <style>
        /* 基础响应式样式 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }
        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表格响应式样式 */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .touch-action-manipulation {
                touch-action: manipulation;
            }
            .tap-highlight-transparent {
                -webkit-tap-highlight-color: transparent;
            }
        }
    </style>
    
</head>
<body class="bg-gray-100 min-h-screen touch-action-manipulation tap-highlight-transparent">
    <!-- 移动端菜单按钮 -->
    <div id="mobile-menu-button" class="fixed z-50 bottom-4 right-4 md:hidden bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
        <i class="iconfont icon-menu text-xl"></i>
    </div>

    <!-- 头部 -->
    

    <!-- 主体内容 -->
    <div class="container mx-auto px-4 py-6">
        
<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-4">应用管理</h1>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-lg p-5 mb-6 border border-gray-100">
        <div class="flex items-center mb-4">
            <i class="iconfont icon-filter text-indigo-500 text-xl mr-2"></i>
            <h2 class="text-lg font-semibold text-gray-800">筛选条件</h2>
        </div>

        <form action="<?php echo url('app/index'); ?>" method="get" id="searchForm">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4">
                <div class="form-group">
                    <label for="title" class="block text-xs font-medium text-gray-700 mb-1">应用标题</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-title text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="title" name="title" value="<?php echo htmlentities((string) $search['title']); ?>"
                            placeholder="输入应用标题"
                            class="block w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                    </div>
                </div>

                <div class="form-group">
                    <label for="appCode" class="block text-xs font-medium text-gray-700 mb-1">应用代码</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-code text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="appCode" name="appCode" value="<?php echo htmlentities((string) $search['appCode']); ?>"
                            placeholder="输入应用代码"
                            class="block w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                    </div>
                </div>

                <div class="form-group">
                    <label for="isOnline" class="block text-xs font-medium text-gray-700 mb-1">上线状态</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-online text-gray-400 text-xs"></i>
                        </div>
                        <select id="isOnline" name="isOnline"
                            class="block w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                            <option value="">全部状态</option>
                            <option value="1" <?php if($search['isOnline'] === '1'): ?>selected<?php endif; ?>>已上线</option>
                            <option value="0" <?php if($search['isOnline'] === '0'): ?>selected<?php endif; ?>>未上线</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="isPrivate" class="block text-xs font-medium text-gray-700 mb-1">私有状态</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-lock text-gray-400 text-xs"></i>
                        </div>
                        <select id="isPrivate" name="isPrivate"
                            class="block w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                            <option value="">全部类型</option>
                            <option value="1" <?php if($search['isPrivate'] === '1'): ?>selected<?php endif; ?>>私有应用</option>
                            <option value="0" <?php if($search['isPrivate'] === '0'): ?>selected<?php endif; ?>>公有应用</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap justify-between mt-5 pt-4 border-t border-gray-100">
                <div class="flex space-x-2 mb-2 sm:mb-0">
                    <button type="submit"
                        class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-search mr-1"></i> 搜索
                    </button>
                    <button type="button" onclick="resetForm()"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-refresh mr-1"></i> 重置
                    </button>
                </div>

                <a href="<?php echo url('app/create'); ?>"
                    class="px-4 py-1.5 text-xs bg-green-600 text-white rounded-md shadow-sm
                    hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2
                    focus:ring-offset-1 focus:ring-green-500 flex items-center">
                    <i class="iconfont icon-add mr-1"></i> 新建应用
                </a>
            </div>
        </form>
    </div>

    <!-- 应用列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- 表格头部 -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="iconfont icon-list mr-2 text-indigo-600"></i>
                    我的应用
                    <span class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                        共 <?php echo htmlentities((string) $list->total()); ?> 个应用
                    </span>
                </h3>
                <div class="flex items-center space-x-3">
                    <!-- 刷新按钮 -->
                    <button type="button" onclick="window.location.reload()" class="px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-all duration-200">
                        <i class="iconfont icon-refresh"></i>
                    </button>
                    <!-- 新建应用按钮 -->
                    <a href="<?php echo url('app/create'); ?>" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all duration-200 flex items-center">
                        <i class="iconfont icon-add mr-1"></i>新建应用
                    </a>
                </div>
            </div>
        </div>

        <!-- 表格内容 -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-app text-gray-400"></i>
                                <span>应用信息</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-status text-gray-400"></i>
                                <span>状态</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden-mobile">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-data text-gray-400"></i>
                                <span>数据统计</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden-mobile">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-time text-gray-400"></i>
                                <span>创建时间</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-setting text-gray-400"></i>
                                <span>操作</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): $i = 0; $__LIST__ = $list;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$item): $mod = ($i % 2 );++$i;?>
                    <tr class="hover:bg-blue-50 transition-colors duration-200 group">
                        <td class="px-6 py-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-14 w-14 relative">
                                    <?php if($item['logo']): ?>
                                    <img class="h-14 w-14 rounded-xl object-cover shadow-sm border border-gray-200" src="//i.cdn.yimenapp.com/<?php echo htmlentities((string) $item['logo']); ?>" alt="<?php echo htmlentities((string) $item['title']); ?>">
                                    <?php else: ?>
                                    <div class="h-14 w-14 rounded-xl bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center shadow-sm border border-gray-200">
                                        <i class="iconfont icon-app text-indigo-500 text-2xl"></i>
                                    </div>
                                    <?php endif; ?>
                                    <!-- 状态指示器 -->
                                    <div class="absolute -top-1 -right-1">
                                        <?php if($item['status'] == 1): ?>
                                        <div class="w-4 h-4 bg-yellow-400 rounded-full border-2 border-white shadow-sm animate-pulse" title="审核中"></div>
                                        <?php elseif($item['status'] == 2 && $item['isOnline']): ?>
                                        <div class="w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm" title="已上线"></div>
                                        <?php else: ?>
                                        <div class="w-4 h-4 bg-gray-300 rounded-full border-2 border-white shadow-sm" title="未上线"></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-sm font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-200"><?php echo htmlentities((string) $item['title']); ?></h4>
                                        <?php if($item['isPrivate']): ?>
                                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="iconfont icon-lock mr-1"></i>私有
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <code class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"><?php echo htmlentities((string) $item['appCode']); ?></code>
                                        <?php if($item['description']): ?>
                                        <span class="text-xs text-gray-500 truncate max-w-xs" title="<?php echo htmlentities((string) $item['description']); ?>"><?php echo htmlentities((string) mb_substr($item['description'],0,30,'utf-8')); ?>...</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <div class="flex flex-col space-y-2">
                                <!-- 审核状态 -->
                                <?php if($item['status'] == 0): ?>
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                    <i class="iconfont icon-edit mr-1.5"></i>编辑中
                                </span>
                                <?php elseif($item['status'] == 1): ?>
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                    <i class="iconfont icon-clock mr-1.5"></i>审核中
                                </span>
                                <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                                    <i class="iconfont icon-check-circle mr-1.5"></i>已通过
                                </span>
                                <?php endif; ?>

                                <!-- 上线状态 -->
                                <?php if($item['isOnline']): ?>
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 border border-indigo-200">
                                    <i class="iconfont icon-online mr-1.5"></i>已上线
                                </span>
                                <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                    <i class="iconfont icon-offline mr-1.5"></i>未上线
                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap hidden-mobile">
                            <div class="text-sm text-gray-900">
                                <div class="flex items-center space-x-4">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-indigo-600"><?php echo htmlentities((string) (isset($item['paidCount']) && ($item['paidCount'] !== '')?$item['paidCount']:0)); ?></div>
                                        <div class="text-xs text-gray-500">付费用户</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-green-600"><?php echo htmlentities((string) (isset($item['downloadCount']) && ($item['downloadCount'] !== '')?$item['downloadCount']:0)); ?></div>
                                        <div class="text-xs text-gray-500">下载量</div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap hidden-mobile">
                            <div class="text-sm text-gray-900"><?php echo htmlentities((string) date('Y-m-d',!is_numeric($item['createTime'])? strtotime($item['createTime']) : $item['createTime'])); ?></div>
                            <div class="text-xs text-gray-500"><?php echo htmlentities((string) date('H:i:s',!is_numeric($item['createTime'])? strtotime($item['createTime']) : $item['createTime'])); ?></div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                <!-- 查看详情 -->
                                <a href="<?php echo url('app/detail', ['appId' => $item['appId']]); ?>"
                                   class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors duration-200">
                                    <i class="iconfont icon-eye mr-1"></i>详情
                                </a>

                                <!-- 编辑 -->
                                <a href="<?php echo url('app/edit', ['appId' => $item['appId']]); ?>"
                                   class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 transition-colors duration-200">
                                    <i class="iconfont icon-edit mr-1"></i>编辑
                                </a>

                                <!-- 删除 -->
                                <button type="button" onclick="deleteApp(<?php echo htmlentities((string) $item['appId']); ?>)"
                                        class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200">
                                    <i class="iconfont icon-delete mr-1"></i>删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </tbody>
            </table>

            <!-- 空状态 -->
            <?php if(empty($list) || (($list instanceof \think\Collection || $list instanceof \think\Paginator ) && $list->isEmpty())): ?>
            <div class="text-center py-12">
                <i class="iconfont icon-empty text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无应用</h3>
                <p class="text-gray-500 mb-4">您还没有创建任何应用</p>
                <a href="<?php echo url('app/create'); ?>" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                    <i class="iconfont icon-add mr-1"></i>创建第一个应用
                </a>
            </div>
            <?php endif; ?>
        </div>

        <!-- 分页 -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center text-sm text-gray-700">
                    <span>显示第 <?php echo htmlentities((string) $list->currentPage()); ?> 页，共 <?php echo htmlentities((string) $list->lastPage()); ?> 页</span>
                    <span class="mx-2">•</span>
                    <span>每页 <?php echo htmlentities((string) $list->listRows()); ?> 条，共 <?php echo htmlentities((string) $list->total()); ?> 条记录</span>
                </div>
                <div class="flex items-center space-x-2">
                    <?php echo htmlentities((string) $list->render()); ?>
                </div>
            </div>
        </div>
    </div>
</div>

    </div>

    <!-- 底部 -->
    <footer class="bg-white shadow mt-8 py-4">
    <div class="container mx-auto px-4">
        <div class="text-center text-gray-500 text-sm">
            &copy; 2025 应用市场管理系统 
        </div>
    </div>
</footer>


    <script src="/static/js/app.js"></script>
    
<script>
// 重置筛选表单
function resetForm() {
    const form = document.getElementById('searchForm');
    const inputs = form.querySelectorAll('input[type="text"], select');

    // 清空所有输入框和下拉框
    inputs.forEach(input => {
        input.value = '';
    });

    // 提交表单
    form.submit();
}

// 删除应用
function deleteApp(appId) {
    if (confirm('确定要删除这个应用吗？此操作不可恢复！')) {
        fetch('<?php echo url("app/delete"); ?>?appId=' + appId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                // 使用更友好的通知
                showNotification(data.msg, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.msg || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('删除失败，请重试', 'error');
        });
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');
        });
    });
});
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 输入框焦点效果 */
.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 表格悬停效果 */
tbody tr:hover {
    background-color: #eff6ff;
}

/* 状态指示器动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐变背景 */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* 按钮悬停效果 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* 表格行高亮 */
tbody tr.group:hover {
    background: linear-gradient(90deg, #eff6ff 0%, #dbeafe 50%, #eff6ff 100%);
}

/* 卡片阴影效果 */
.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 状态标签样式 */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

/* 搜索表单优化 */
.form-group input:focus,
.form-group select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-5 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }

    /* 移动端隐藏部分列 */
    .hidden-mobile {
        display: none;
    }
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
    height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 文本截断 */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表格固定高度 */
.table-container {
    max-height: 600px;
    overflow-y: auto;
}

/* 分页样式优化 */
.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination a,
.pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2rem;
    height: 2rem;
    padding: 0 0.5rem;
    text-decoration: none;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.pagination a:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.pagination .current {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
}

.pagination .disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>

</body>
</html>
