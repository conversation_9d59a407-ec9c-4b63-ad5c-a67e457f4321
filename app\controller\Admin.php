<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\App as AppModel;
use think\exception\ValidateException;

/**
 * 后台管理控制器
 */
class Admin extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\AdminAuth',
    ];

    /**
     * 后台首页 - App审核列表
     */
    public function index(Request $request)
    {
        // 获取搜索参数
        $search = [
            'title' => $request->param('title', ''),
            'appCode' => $request->param('appCode', ''),
            'status' => $request->param('status', ''),
            'isOnline' => $request->param('isOnline', ''),
            'isPrivate' => $request->param('isPrivate', ''),
            'salerUserId' => $request->param('salerUserId', ''),
        ];

        // 构建查询
        $query = AppModel::withSearch(['title', 'appCode', 'isOnline', 'isPrivate'], $search);
        
        // 状态筛选
        if ($search['status'] !== '') {
            $query->where('status', $search['status']);
        }
        
        // 开发者筛选
        if ($search['salerUserId'] !== '') {
            $query->where('salerUserId', $search['salerUserId']);
        }

        // 查询App列表
        $list = $query->order('createTime', 'desc')
            ->paginate([
                'list_rows' => 15,
                'query' => $request->param(),
            ]);

        // 获取统计数据
        $stats = [
            'total' => AppModel::count(),
            'pending' => AppModel::where('status', 1)->count(),
            'approved' => AppModel::where('status', 2)->count(),
            'draft' => AppModel::where('status', 0)->count(),
            'online' => AppModel::where('isOnline', 1)->count(),
        ];

        // 模板赋值
        View::assign([
            'list' => $list,
            'search' => $search,
            'stats' => $stats,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * App详情页面
     */
    public function detail(Request $request)
    {
        $appId = $request->param('appId');
        
        if (!$appId) {
            return $this->error('缺少应用ID');
        }
        
        // 查询App信息
        $app = AppModel::find($appId);
        
        if (!$app) {
            return $this->error('应用不存在');
        }
        
        // 获取运行时环境数量
        $runtimeCount = $app->runtimes()->count();
        
        // 获取应用包数量
        $buildCount = $app->builds()->count();
        
        // 模板赋值
        View::assign([
            'app' => $app,
            'runtimeCount' => $runtimeCount,
            'buildCount' => $buildCount,
        ]);
        
        // 渲染模板
        return View::fetch();
    }

    /**
     * 审核App
     */
    public function review(Request $request)
    {
        $appId = $request->param('appId');
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');
        
        if (!$appId || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 查询App信息
        $app = AppModel::find($appId);
        
        if (!$app) {
            return json(['code' => 0, 'msg' => '应用不存在']);
        }
        
        // 只能审核状态为"审核中"的应用
        if ($app->status != 1) {
            return json(['code' => 0, 'msg' => '该应用不在审核状态']);
        }
        
        // 更新状态
        if ($action === 'approve') {
            $app->status = 2; // 审核通过
            $app->statusMessage = $message ?: '审核通过';
        } else {
            $app->status = 0; // 审核拒绝，回到编辑状态
            $app->isOnline = 0; // 下线
            $app->statusMessage = $message ?: '审核拒绝';
        }
        
        $app->save();
        
        $actionText = $action === 'approve' ? '通过' : '拒绝';
        return json(['code' => 1, 'msg' => "审核{$actionText}成功"]);
    }

    /**
     * 批量审核
     */
    public function batchReview(Request $request)
    {
        $appIds = $request->param('appIds', []);
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');
        
        if (empty($appIds) || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 查询要审核的应用
        $apps = AppModel::whereIn('appId', $appIds)
            ->where('status', 1) // 只处理审核中的应用
            ->select();
        
        if ($apps->isEmpty()) {
            return json(['code' => 0, 'msg' => '没有可审核的应用']);
        }
        
        // 批量更新
        $updateData = [];
        if ($action === 'approve') {
            $updateData = [
                'status' => 2,
                'statusMessage' => $message ?: '批量审核通过'
            ];
        } else {
            $updateData = [
                'status' => 0,
                'isOnline' => 0,
                'statusMessage' => $message ?: '批量审核拒绝'
            ];
        }
        
        AppModel::whereIn('appId', $appIds)
            ->where('status', 1)
            ->update($updateData);
        
        $actionText = $action === 'approve' ? '通过' : '拒绝';
        return json(['code' => 1, 'msg' => "批量审核{$actionText}成功，共处理{$apps->count()}个应用"]);
    }

    /**
     * 更新App状态（上线/下线）
     */
    public function updateStatus(Request $request)
    {
        $appId = $request->param('appId');
        $isOnline = $request->param('isOnline');
        
        if (!$appId || !in_array($isOnline, [0, 1, '0', '1'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 查询App信息
        $app = AppModel::find($appId);
        
        if (!$app) {
            return json(['code' => 0, 'msg' => '应用不存在']);
        }
        
        // 只有审核通过的应用才能上线
        if ($isOnline == 1 && $app->status != 2) {
            return json(['code' => 0, 'msg' => '只有审核通过的应用才能上线']);
        }
        
        $app->isOnline = (int)$isOnline;
        $app->save();
        
        $statusText = $isOnline ? '上线' : '下线';
        return json(['code' => 1, 'msg' => "{$statusText}成功"]);
    }
}
