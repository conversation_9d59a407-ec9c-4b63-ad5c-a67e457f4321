<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\App as AppModel;
use app\model\AppBuild as AppBuildModel;
use app\model\AppRuntime as AppRuntimeModel;
use think\exception\ValidateException;

/**
 * 后台管理控制器
 */
class Admin extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\AdminAuth',
    ];

    /**
     * 后台管理概览页面
     */
    public function index(Request $request)
    {
        // 获取统计数据
        $stats = [
            'total' => AppModel::count(),
            'pending' => AppModel::where('status', 1)->count() +
                        AppBuildModel::where('status', 1)->count() +
                        AppRuntimeModel::where('status', 1)->count(),
            'approved' => AppModel::where('status', 2)->count(),
            'draft' => AppModel::where('status', 0)->count(),
            'online' => AppModel::where('isOnline', 1)->count(),
        ];

        // 模板赋值
        View::assign([
            'stats' => $stats,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * App详情页面
     */
    public function detail(Request $request)
    {
        $appId = $request->param('appId');

        if (!$appId) {
            return $this->error('缺少应用ID');
        }

        // 查询App信息
        $app = AppModel::find($appId);

        if (!$app) {
            return $this->error('应用不存在');
        }

        // 获取运行时环境数量
        $runtimeCount = $app->runtimes()->count();

        // 获取应用包数量
        $buildCount = $app->builds()->count();

        // 模板赋值
        View::assign([
            'app' => $app,
            'runtimeCount' => $runtimeCount,
            'buildCount' => $buildCount,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 审核App
     */
    public function review(Request $request)
    {
        $appId = $request->param('appId');
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');

        if (!$appId || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 查询App信息
        $app = AppModel::find($appId);

        if (!$app) {
            return json(['code' => 0, 'msg' => '应用不存在']);
        }

        // 只能审核状态为"审核中"的应用
        if ($app->status != 1) {
            return json(['code' => 0, 'msg' => '该应用不在审核状态']);
        }

        // 更新状态
        if ($action === 'approve') {
            $app->status = 2; // 审核通过
            $app->statusMessage = $message ?: '审核通过';
        } else {
            $app->status = 0; // 审核拒绝，回到编辑状态
            $app->isOnline = 0; // 下线
            $app->statusMessage = $message ?: '审核拒绝';
        }

        $app->save();

        $actionText = $action === 'approve' ? '通过' : '拒绝';
        return json(['code' => 1, 'msg' => "审核{$actionText}成功"]);
    }

    /**
     * 批量审核
     */
    public function batchReview(Request $request)
    {
        $appIds = $request->param('appIds', []);
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');

        if (empty($appIds) || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 查询要审核的应用
        $apps = AppModel::whereIn('appId', $appIds)
            ->where('status', 1) // 只处理审核中的应用
            ->select();

        if ($apps->isEmpty()) {
            return json(['code' => 0, 'msg' => '没有可审核的应用']);
        }

        // 批量更新
        $updateData = [];
        if ($action === 'approve') {
            $updateData = [
                'status' => 2,
                'statusMessage' => $message ?: '批量审核通过'
            ];
        } else {
            $updateData = [
                'status' => 0,
                'isOnline' => 0,
                'statusMessage' => $message ?: '批量审核拒绝'
            ];
        }

        AppModel::whereIn('appId', $appIds)
            ->where('status', 1)
            ->update($updateData);

        $actionText = $action === 'approve' ? '通过' : '拒绝';
        return json(['code' => 1, 'msg' => "批量审核{$actionText}成功，共处理{$apps->count()}个应用"]);
    }

    /**
     * 更新App状态（上线/下线）
     */
    public function updateStatus(Request $request)
    {
        $appId = $request->param('appId');
        $isOnline = $request->param('isOnline');

        if (!$appId || !in_array($isOnline, [0, 1, '0', '1'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 查询App信息
        $app = AppModel::find($appId);

        if (!$app) {
            return json(['code' => 0, 'msg' => '应用不存在']);
        }

        // 只有审核通过的应用才能上线
        if ($isOnline == 1 && $app->status != 2) {
            return json(['code' => 0, 'msg' => '只有审核通过的应用才能上线']);
        }

        $app->isOnline = (int)$isOnline;
        $app->save();

        $statusText = $isOnline ? '上线' : '下线';
        return json(['code' => 1, 'msg' => "{$statusText}成功"]);
    }

    /**
     * 审核中心 - 统一显示所有待审核项目
     */
    public function reviewCenter(Request $request)
    {
        // 获取搜索参数
        $search = [
            'type' => $request->param('type', ''), // app, build, runtime
            'status' => $request->param('status', '1'), // 默认显示审核中的
            'keyword' => $request->param('keyword', ''),
        ];

        $reviewItems = [];

        // 查询待审核的App
        if (empty($search['type']) || $search['type'] === 'app') {
            $appQuery = AppModel::where('status', $search['status']);
            if ($search['keyword']) {
                $appQuery->where('title|appCode', 'like', "%{$search['keyword']}%");
            }
            $apps = $appQuery->order('createTime', 'desc')->select();

            foreach ($apps as $app) {
                $reviewItems[] = [
                    'type' => 'app',
                    'id' => $app->appId,
                    'title' => $app->title,
                    'code' => $app->appCode,
                    'description' => $app->description,
                    'status' => $app->status,
                    'statusMessage' => $app->statusMessage,
                    'createTime' => $app->createTime,
                    'salerUserId' => $app->salerUserId,
                    'logo' => $app->logo,
                    'isPrivate' => $app->isPrivate,
                ];
            }
        }

        // 查询待审核的AppBuild
        if (empty($search['type']) || $search['type'] === 'build') {
            $buildQuery = AppBuildModel::with('app')->where('status', $search['status']);
            if ($search['keyword']) {
                $buildQuery->whereHas('app', function($query) use ($search) {
                    $query->where('title|appCode', 'like', "%{$search['keyword']}%");
                })->whereOr('versionName|description', 'like', "%{$search['keyword']}%");
            }
            $builds = $buildQuery->order('createTime', 'desc')->select();

            foreach ($builds as $build) {
                $reviewItems[] = [
                    'type' => 'build',
                    'id' => $build->appBuildId,
                    'title' => $build->app->title . ' - v' . $build->versionName,
                    'code' => $build->app->appCode,
                    'description' => $build->description,
                    'status' => $build->status,
                    'statusMessage' => $build->statusMessage,
                    'createTime' => $build->createTime,
                    'salerUserId' => $build->app->salerUserId,
                    'logo' => $build->app->logo,
                    'versionCode' => $build->versionCode,
                    'versionName' => $build->versionName,
                    'appId' => $build->appId,
                ];
            }
        }

        // 查询待审核的AppRuntime
        if (empty($search['type']) || $search['type'] === 'runtime') {
            $runtimeQuery = AppRuntimeModel::with('app')->where('status', $search['status']);
            if ($search['keyword']) {
                $runtimeQuery->whereHas('app', function($query) use ($search) {
                    $query->where('title|appCode', 'like', "%{$search['keyword']}%");
                });
            }
            $runtimes = $runtimeQuery->order('createTime', 'desc')->select();

            foreach ($runtimes as $runtime) {
                $reviewItems[] = [
                    'type' => 'runtime',
                    'id' => $runtime->appRuntimeId,
                    'title' => $runtime->app->title . ' - ' . $runtime->runtimeName,
                    'code' => $runtime->app->appCode,
                    'description' => $runtime->content ? strip_tags($runtime->content) : '运行时环境配置',
                    'status' => $runtime->status,
                    'statusMessage' => $runtime->statusMessage,
                    'createTime' => $runtime->createTime,
                    'salerUserId' => $runtime->app->salerUserId,
                    'logo' => $runtime->app->logo,
                    'runtimeId' => $runtime->runtimeId,
                    'runtimeName' => $runtime->runtimeName,
                    'appId' => $runtime->appId,
                ];
            }
        }

        // 按创建时间排序
        usort($reviewItems, function($a, $b) {
            return strtotime($b['createTime']) - strtotime($a['createTime']);
        });

        // 手动分页
        $page = $request->param('page', 1);
        $listRows = 15;
        $total = count($reviewItems);
        $offset = ($page - 1) * $listRows;
        $items = array_slice($reviewItems, $offset, $listRows);

        // 构造分页对象
        $list = new \think\paginator\driver\Bootstrap($items, $listRows, $page, $total, false, [
            'path' => $request->baseUrl(),
            'query' => $request->param(),
        ]);

        // 获取统计数据
        $stats = [
            'total' => AppModel::where('status', 1)->count() +
                      AppBuildModel::where('status', 1)->count() +
                      AppRuntimeModel::where('status', 1)->count(),
            'app' => AppModel::where('status', 1)->count(),
            'build' => AppBuildModel::where('status', 1)->count(),
            'runtime' => AppRuntimeModel::where('status', 1)->count(),
        ];

        // 模板赋值
        View::assign([
            'list' => $list,
            'search' => $search,
            'stats' => $stats,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 审核AppBuild
     */
    public function reviewBuild(Request $request)
    {
        $buildId = $request->param('buildId');
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');

        if (!$buildId || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 查询AppBuild信息
        $build = AppBuildModel::find($buildId);

        if (!$build) {
            return json(['code' => 0, 'msg' => '应用包不存在']);
        }

        // 只能审核状态为"审核中"的应用包
        if ($build->status != 1) {
            return json(['code' => 0, 'msg' => '该应用包不在审核状态']);
        }

        // 更新状态
        if ($action === 'approve') {
            $build->status = 2; // 审核通过
            $build->statusMessage = $message ?: '审核通过';
        } else {
            $build->status = 0; // 审核拒绝，回到编辑状态
            $build->statusMessage = $message ?: '审核拒绝';
        }

        $build->save();

        $actionText = $action === 'approve' ? '通过' : '拒绝';
        return json(['code' => 1, 'msg' => "应用包审核{$actionText}成功"]);
    }

    /**
     * 审核AppRuntime
     */
    public function reviewRuntime(Request $request)
    {
        $runtimeId = $request->param('runtimeId');
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');

        if (!$runtimeId || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 查询AppRuntime信息
        $runtime = AppRuntimeModel::find($runtimeId);

        if (!$runtime) {
            return json(['code' => 0, 'msg' => '运行时环境不存在']);
        }

        // 只能审核状态为"审核中"的运行时环境
        if ($runtime->status != 1) {
            return json(['code' => 0, 'msg' => '该运行时环境不在审核状态']);
        }

        // 更新状态
        if ($action === 'approve') {
            $runtime->status = 2; // 审核通过
            $runtime->statusMessage = $message ?: '审核通过';
        } else {
            $runtime->status = 0; // 审核拒绝，回到编辑状态
            $runtime->isOnline = 0; // 下线
            $runtime->statusMessage = $message ?: '审核拒绝';
        }

        $runtime->save();

        $actionText = $action === 'approve' ? '通过' : '拒绝';
        return json(['code' => 1, 'msg' => "运行时环境审核{$actionText}成功"]);
    }

    /**
     * 通用审核方法
     */
    public function reviewItem(Request $request)
    {
        $type = $request->param('type'); // app, build, runtime
        $id = $request->param('id');
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');

        if (!$type || !$id || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        switch ($type) {
            case 'app':
                return $this->review($request->withInput(['appId' => $id, 'action' => $action, 'message' => $message]));
            case 'build':
                return $this->reviewBuild($request->withInput(['buildId' => $id, 'action' => $action, 'message' => $message]));
            case 'runtime':
                return $this->reviewRuntime($request->withInput(['runtimeId' => $id, 'action' => $action, 'message' => $message]));
            default:
                return json(['code' => 0, 'msg' => '不支持的审核类型']);
        }
    }

    /**
     * 批量审核（支持混合类型）
     */
    public function batchReviewItems(Request $request)
    {
        $items = $request->param('items', []); // [['type' => 'app', 'id' => 1], ...]
        $action = $request->param('action'); // approve 或 reject
        $message = $request->param('message', '');

        if (empty($items) || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $successCount = 0;
        $errors = [];

        foreach ($items as $item) {
            if (!isset($item['type']) || !isset($item['id'])) {
                continue;
            }

            try {
                $result = $this->reviewItem($request->withInput([
                    'type' => $item['type'],
                    'id' => $item['id'],
                    'action' => $action,
                    'message' => $message
                ]));

                $resultData = json_decode($result->getContent(), true);
                if ($resultData['code'] === 1) {
                    $successCount++;
                } else {
                    $errors[] = $resultData['msg'];
                }
            } catch (\Exception $e) {
                $errors[] = "处理{$item['type']}#{$item['id']}时出错：" . $e->getMessage();
            }
        }

        $actionText = $action === 'approve' ? '通过' : '拒绝';
        $message = "批量审核{$actionText}完成，成功处理{$successCount}项";

        if (!empty($errors)) {
            $message .= "，失败" . count($errors) . "项";
        }

        return json(['code' => 1, 'msg' => $message, 'details' => $errors]);
    }
}
