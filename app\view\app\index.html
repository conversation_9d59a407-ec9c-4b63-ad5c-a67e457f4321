{extend name="common/base" /}

{block name="title"}应用列表 - 应用市场管理系统{/block}

{block name="content"}
<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-4">应用管理</h1>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-lg p-5 mb-6 border border-gray-100">
        <div class="flex items-center mb-4">
            <i class="iconfont icon-filter text-indigo-500 text-xl mr-2"></i>
            <h2 class="text-lg font-semibold text-gray-800">筛选条件</h2>
        </div>

        <form action="{__MPRE__}{:url('app/index')}" method="get" id="searchForm">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4">
                <div class="form-group">
                    <label for="title" class="block text-xs font-medium text-gray-700 mb-1">应用标题</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-title text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="title" name="title" value="{$search.title}"
                            placeholder="输入应用标题"
                            class="block w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                    </div>
                </div>

                <div class="form-group">
                    <label for="appCode" class="block text-xs font-medium text-gray-700 mb-1">应用代码</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-code text-gray-400 text-xs"></i>
                        </div>
                        <input type="text" id="appCode" name="appCode" value="{$search.appCode}"
                            placeholder="输入应用代码"
                            class="block w-full pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-md
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                    </div>
                </div>

                <div class="form-group">
                    <label for="isOnline" class="block text-xs font-medium text-gray-700 mb-1">上线状态</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-online text-gray-400 text-xs"></i>
                        </div>
                        <select id="isOnline" name="isOnline"
                            class="block w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                            <option value="">全部状态</option>
                            <option value="1" {if $search.isOnline === '1'}selected{/if}>已上线</option>
                            <option value="0" {if $search.isOnline === '0'}selected{/if}>未上线</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="isPrivate" class="block text-xs font-medium text-gray-700 mb-1">私有状态</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-lock text-gray-400 text-xs"></i>
                        </div>
                        <select id="isPrivate" name="isPrivate"
                            class="block w-full pl-10 pr-8 py-1.5 text-sm border border-gray-300 rounded-md appearance-none
                            focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                            hover:border-indigo-300 transition-colors duration-200">
                            <option value="">全部类型</option>
                            <option value="1" {if $search.isPrivate === '1'}selected{/if}>私有应用</option>
                            <option value="0" {if $search.isPrivate === '0'}selected{/if}>公有应用</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="iconfont icon-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap justify-between mt-5 pt-4 border-t border-gray-100">
                <div class="flex space-x-2 mb-2 sm:mb-0">
                    <button type="submit"
                        class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                        hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                        <i class="iconfont icon-search mr-1"></i> 搜索
                    </button>
                    <button type="button" onclick="resetForm()"
                        class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                        hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                        focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                        <i class="iconfont icon-refresh mr-1"></i> 重置
                    </button>
                </div>

                <a href="{__MPRE__}{:url('app/create')}"
                    class="px-4 py-1.5 text-xs bg-green-600 text-white rounded-md shadow-sm
                    hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2
                    focus:ring-offset-1 focus:ring-green-500 flex items-center">
                    <i class="iconfont icon-add mr-1"></i> 新建应用
                </a>
            </div>
        </form>
    </div>

    <!-- 应用列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- 表格头部 -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="iconfont icon-list mr-2 text-indigo-600"></i>
                    我的应用
                    <span class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                        共 {$list->total()} 个应用
                    </span>
                </h3>
                <div class="flex items-center space-x-3">
                    <!-- 刷新按钮 -->
                    <button type="button" onclick="window.location.reload()" class="px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-600 hover:text-gray-900 hover:border-gray-400 transition-all duration-200">
                        <i class="iconfont icon-refresh"></i>
                    </button>
                    <!-- 新建应用按钮 -->
                    <a href="{__MPRE__}{:url('app/create')}" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all duration-200 flex items-center">
                        <i class="iconfont icon-add mr-1"></i>新建应用
                    </a>
                </div>
            </div>
        </div>

        <!-- 表格内容 -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-app text-gray-400"></i>
                                <span>应用信息</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-status text-gray-400"></i>
                                <span>状态</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden-mobile">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-data text-gray-400"></i>
                                <span>数据统计</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden-mobile">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-time text-gray-400"></i>
                                <span>创建时间</span>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div class="flex items-center space-x-1">
                                <i class="iconfont icon-setting text-gray-400"></i>
                                <span>操作</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {volist name="list" id="item"}
                    <tr class="hover:bg-blue-50 transition-colors duration-200 group">
                        <td class="px-6 py-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-14 w-14 relative">
                                    {if $item.logo}
                                    <img class="h-14 w-14 rounded-xl object-cover shadow-sm border border-gray-200" src="//i.cdn.yimenapp.com/{$item.logo}" alt="{$item.title}">
                                    {else}
                                    <div class="h-14 w-14 rounded-xl bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center shadow-sm border border-gray-200">
                                        <i class="iconfont icon-app text-indigo-500 text-2xl"></i>
                                    </div>
                                    {/if}
                                    <!-- 状态指示器 -->
                                    <div class="absolute -top-1 -right-1">
                                        {if $item.status == 1}
                                        <div class="w-4 h-4 bg-yellow-400 rounded-full border-2 border-white shadow-sm animate-pulse" title="审核中"></div>
                                        {elseif $item.status == 2 && $item.isOnline}
                                        <div class="w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-sm" title="已上线"></div>
                                        {else}
                                        <div class="w-4 h-4 bg-gray-300 rounded-full border-2 border-white shadow-sm" title="未上线"></div>
                                        {/if}
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-sm font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">{$item.title}</h4>
                                        {if $item.isPrivate}
                                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="iconfont icon-lock mr-1"></i>私有
                                        </span>
                                        {/if}
                                    </div>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <code class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{$item.appCode}</code>
                                        {if $item.description}
                                        <span class="text-xs text-gray-500 truncate max-w-xs" title="{$item.description}">{$item.description|mb_substr=0,30,'utf-8'}...</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <div class="flex flex-col space-y-2">
                                <!-- 审核状态 -->
                                {if $item.status == 0}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                    <i class="iconfont icon-edit mr-1.5"></i>编辑中
                                </span>
                                {elseif $item.status == 1}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                    <i class="iconfont icon-clock mr-1.5"></i>审核中
                                </span>
                                {else}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                                    <i class="iconfont icon-check-circle mr-1.5"></i>已通过
                                </span>
                                {/if}

                                <!-- 上线状态 -->
                                {if $item.isOnline}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 border border-indigo-200">
                                    <i class="iconfont icon-online mr-1.5"></i>已上线
                                </span>
                                {else}
                                <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                    <i class="iconfont icon-offline mr-1.5"></i>未上线
                                </span>
                                {/if}
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap hidden-mobile">
                            <div class="text-sm text-gray-900">
                                <div class="flex items-center space-x-4">
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-indigo-600">{$item.paidCount|default=0}</div>
                                        <div class="text-xs text-gray-500">付费用户</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-lg font-semibold text-green-600">{$item.downloadCount|default=0}</div>
                                        <div class="text-xs text-gray-500">下载量</div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap hidden-mobile">
                            <div class="text-sm text-gray-900">{$item.createTime|date='Y-m-d'}</div>
                            <div class="text-xs text-gray-500">{$item.createTime|date='H:i:s'}</div>
                        </td>
                        <td class="px-6 py-5 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                <!-- 查看详情 -->
                                <a href="{__MPRE__}{:url('app/detail', ['appId' => $item.appId])}"
                                   class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors duration-200">
                                    <i class="iconfont icon-eye mr-1"></i>详情
                                </a>

                                <!-- 编辑 -->
                                <a href="{__MPRE__}{:url('app/edit', ['appId' => $item.appId])}"
                                   class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 transition-colors duration-200">
                                    <i class="iconfont icon-edit mr-1"></i>编辑
                                </a>

                                <!-- 删除 -->
                                <button type="button" onclick="deleteApp({$item.appId})"
                                        class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200">
                                    <i class="iconfont icon-delete mr-1"></i>删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>

            <!-- 空状态 -->
            {empty name="list"}
            <div class="text-center py-12">
                <i class="iconfont icon-empty text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无应用</h3>
                <p class="text-gray-500 mb-4">您还没有创建任何应用</p>
                <a href="{__MPRE__}{:url('app/create')}" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                    <i class="iconfont icon-add mr-1"></i>创建第一个应用
                </a>
            </div>
            {/empty}
        </div>

        <!-- 分页 -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center text-sm text-gray-700">
                    <span>显示第 {$list->currentPage()} 页，共 {$list->lastPage()} 页</span>
                    <span class="mx-2">•</span>
                    <span>每页 {$list->listRows()} 条，共 {$list->total()} 条记录</span>
                </div>
                <div class="flex items-center space-x-2">
                    {$list->render()}
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 重置筛选表单
function resetForm() {
    const form = document.getElementById('searchForm');
    const inputs = form.querySelectorAll('input[type="text"], select');

    // 清空所有输入框和下拉框
    inputs.forEach(input => {
        input.value = '';
    });

    // 提交表单
    form.submit();
}

// 删除应用
function deleteApp(appId) {
    if (confirm('确定要删除这个应用吗？此操作不可恢复！')) {
        fetch('{__MPRE__}{:url("app/delete")}?appId=' + appId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                // 使用更友好的通知
                showNotification(data.msg, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.msg || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('删除失败，请重试', 'error');
        });
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group')?.classList.remove('is-focused');
        });
    });
});
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 输入框焦点效果 */
.form-group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select {
    transition: all 0.2s ease-in-out;
}

/* 通知动画 */
#notification {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

/* 表格悬停效果 */
tbody tr:hover {
    background-color: #eff6ff;
}

/* 状态指示器动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐变背景 */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* 按钮悬停效果 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* 表格行高亮 */
tbody tr.group:hover {
    background: linear-gradient(90deg, #eff6ff 0%, #dbeafe 50%, #eff6ff 100%);
}

/* 卡片阴影效果 */
.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 状态标签样式 */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

/* 搜索表单优化 */
.form-group input:focus,
.form-group select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-5 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }

    /* 移动端隐藏部分列 */
    .hidden-mobile {
        display: none;
    }
}

/* 滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
    height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 文本截断 */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表格固定高度 */
.table-container {
    max-height: 600px;
    overflow-y: auto;
}

/* 分页样式优化 */
.pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination a,
.pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2rem;
    height: 2rem;
    padding: 0 0.5rem;
    text-decoration: none;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.pagination a:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.pagination .current {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
}

.pagination .disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>
{/block}
